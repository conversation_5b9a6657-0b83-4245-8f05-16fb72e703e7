@use 'sass:color';
@use '../../styles/variables' as vars;

$shadow-color: rgba(0, 0, 0, 0.04);
$shadow-color-hover: rgba(0, 0, 0, 0.08);

.dashboard-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: vars.$primary-color;
  overflow: hidden;
}

.dashboard__content {
  flex: 1;
  display: flex;
  overflow: hidden;
  min-height: 0;
}

.dashboard__main {
  flex: 1;
  background-color: vars.$primary-color;
  transition: all 0.3s ease;
  margin-right: 400px;
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &.dashboard__main--voice-collapsed {
    margin-right: 0;
  }
}

.dashboard__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: vars.$spacing-lg vars.$spacing-lg 0;
  flex-shrink: 0;
}

.dashboard__breadcrumb {
  // Container for the Breadcrumb component
  // The component handles its own styling
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-xs;
}

.dashboard__bucket-info {
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
  font-size: vars.$font-size-sm;
  color: vars.$color-gray-600;
}

.dashboard__bucket-label {
  font-weight: vars.$font-weight-medium;
}

.dashboard__bucket-name {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background-color: vars.$color-gray-100;
  padding: 2px 6px;
  border-radius: vars.$border-radius-sm;
  color: vars.$color-gray-700;
}

.dashboard__bucket-error {
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
  font-size: vars.$font-size-sm;
  color: vars.$color-red-600;
}

.dashboard__bucket-error-text {
  font-weight: vars.$font-weight-medium;
}

.dashboard__actions {
  display: flex;
  gap: vars.$spacing-sm;
}

.dashboard__grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: vars.$spacing-md;
  padding: vars.$spacing-xs;

  &--files-only {
    margin-top: vars.$spacing-md;
  }
}

.dashboard__content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0 vars.$spacing-lg;
  position: relative;
}

.dashboard__split-panels {
  position: relative;
  width: 100%;
  height: 100%;
}

.dashboard__top-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: var(--top-panel-height, 50vh); // Use CSS custom property with fallback
  overflow: hidden;
  z-index: 1;
  transition: height 0.1s ease;

  &.dashboard__top-panel--voice-collapsed {
    right: 0;
  }
}

.dashboard__bottom-panel {
  position: absolute;
  top: var(--bottom-panel-top, 50vh); // Use CSS custom property with fallback
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  transition: top 0.1s ease;

  &.dashboard__bottom-panel--voice-collapsed {
    right: 0;
  }
}

.dashboard__drag-handle {
  position: absolute;
  top: var(--drag-handle-top, calc(50vh - 5px)); // Use CSS custom property with fallback
  left: 0;
  right: 0;
  height: 10px;
  cursor: row-resize;
  z-index: 3;
  background-color: white;
  border-top: 1px solid vars.$border-color;
  transition: top 0.1s ease;
}

.dashboard__drag-indicator {
  width: 48px;
  height: 6px;
  background-color: vars.$color-gray-300;
  border-radius: 3px;
  margin: 2px auto;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: vars.$color-gray-600;
  }
}

.dashboard__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: vars.$spacing-xl;
  color: vars.$color-gray-500;

  &-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid vars.$border-color;
    border-top-color: vars.$primary-btn-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: vars.$spacing-md;
  }

  &-text {
    font-size: vars.$font-size-base;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.dashboard__empty-state {
  text-align: center;
  padding: vars.$spacing-xl;
  color: vars.$color-gray-500;

  .dashboard__empty-title {
    font-size: vars.$font-size-base;
    font-weight: vars.$font-weight-medium;
    margin-bottom: vars.$spacing-xs;
  }

  .dashboard__empty-desc {
    font-size: vars.$font-size-sm;
  }
}

.dashboard__actions-section {
  padding: vars.$spacing-md vars.$spacing-xs;
  margin-bottom: vars.$spacing-md;
}

.dashboard__audit-container {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-sm;
}

.dashboard__audit-progress {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-xs;
  padding: vars.$spacing-sm;
  background-color: vars.$color-gray-100;
  border-radius: vars.$border-radius-sm;
  font-size: vars.$font-size-sm;
}

.audit-progress__message {
  color: vars.$color-gray-600;
  font-weight: vars.$font-weight-medium;
}

.audit-progress__bar {
  position: relative;
  width: 100%;
  height: 8px;
  background-color: vars.$color-gray-200;
  border-radius: 4px;
  overflow: hidden;
}

.audit-progress__bar-fill {
  height: 100%;
  background-color: vars.$primary-btn-color;
  transition: width 0.3s ease;
}

.audit-progress__bar-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: vars.$font-size-xs;
  font-weight: vars.$font-weight-medium;
  color: vars.$color-gray-700;
} 