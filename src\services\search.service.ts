import { MissingIndexError } from '@/types/errors';
import { getJWTToken } from '@/utils/auth.utils';
import { getOrganizationId } from './organization.service';

const BASE_URL = import.meta.env.VITE_EXPLAIN_BACKEND_URL;

export interface SearchHit {
  score: number;
  id: string;
  document_name: string;
  chunk_content: string;
}

export interface SearchResponse {
  hits: SearchHit[];
  total_hits: number;
  filters_used: Array<Record<string, unknown>>;
}

export interface SearchRequest {
  query: string;
  company: string;
}

export interface CreateIndexRequest {
  project_name: string;
}

export interface CreateIndexResponse {
  success: boolean;
  index_exists: boolean;
  index_name: string;
  message: string;
}

export const search = async (request: SearchRequest): Promise<SearchResponse> => {
  try {
    const organizationId = getOrganizationId();
    const jwtToken = getJWTToken();

    const response = await fetch(`${BASE_URL}/api/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${jwtToken}`,
        'X-Organization-Id': organizationId,
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      if (response.status === 400) {
        const errorData = await response.json();
        throw new MissingIndexError(errorData.details);
      }
      throw new Error(`Search request failed with status ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    if (error instanceof MissingIndexError) {
      console.error('Bad request error:', error);
      throw error;
    }
    console.error('Search error:', error);
    throw new Error(`Search failed: ${error instanceof Error ? error.message : String(error)}`);
  }
};

export const createIndex = async (request: CreateIndexRequest): Promise<CreateIndexResponse> => {
  try {
    const organizationId = getOrganizationId();
    const jwtToken = getJWTToken();

    const response = await fetch(`${BASE_URL}/api/search/create-index`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${jwtToken}`,
        'X-Organization-Id': organizationId,
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`Create index request failed with status ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Create index error:', error);
    throw new Error(`Create index failed: ${error instanceof Error ? error.message : String(error)}`);
  }
};
