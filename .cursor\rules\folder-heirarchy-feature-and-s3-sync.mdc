---
description: 
globs: 
alwaysApply: true
---
# Feature: Folder Hierarchy & S3 Sync

The app mimics a drive-style interface with a deeply nested folder structure:

- Root displays a list of user-created Projects
- Projects contain Year folders (e.g., 2024), which contain Month folders (e.g., July)
- Each Month folder automatically generates:
  - General Ledger
  - Source Documents
  - Output (read-only)

This folder structure must be synced with AWS S3. All folder creation, listing, and uploads must go through `services/file-storage.service.ts` using stubbed functions initially. File state in the UI should reflect the S3 path structure accurately.