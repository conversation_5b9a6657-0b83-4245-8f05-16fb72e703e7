import React, { useState, useEffect } from 'react';
import FolderTabs from '@/components/ui/FolderTabs/FolderTabs';
import FileItem from '@/components/ui/FileItem/FileItem';
import ViewToggle, { ViewMode } from '@/components/ui/ViewToggle/ViewToggle';
import { NavigableFolderItem } from '@/types';
import { useFileStorage } from '@/hooks/useFileStorage';
import './OutputPanel.scss';

interface OutputPanelProps {
  dataSource: string;
  basePath: string;
  onFileClick: (file: any) => void;
  showAddButton?: boolean;
  onAddNew?: () => void;
}

const OutputPanel: React.FC<OutputPanelProps> = ({
  dataSource,
  basePath,
  onFileClick,
  showAddButton = false,
  onAddNew,
}) => {
  const [activeTab, setActiveTab] = useState<string>('');
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  
  const { files: allFiles, folders: allFolders } = useFileStorage();
  
  // Calculate the data source path
  const dataSourcePath = `${basePath}/${dataSource}`.replace(/\/+/g, '/');
  
  // Get folders for the data source
  const outputsFolders: NavigableFolderItem[] = allFolders
    .filter(folder => 
      folder.path.startsWith(dataSourcePath) && 
      folder.path !== dataSourcePath &&
      folder.path.split('/').length === dataSourcePath.split('/').length + 1
    )
    .map(folder => ({
      name: folder.name,
      path: folder.path,
      type: 'folder' as const
    }));
  
  // Create dummy "Pending" tab if no folders exist
  const displayFolders = outputsFolders.length > 0 ? outputsFolders : [{
    name: 'Pending',
    path: `${dataSourcePath}/pending`,
    type: 'folder' as const
  }];
  
  // Set active tab to first folder if none selected
  useEffect(() => {
    if (displayFolders.length > 0 && !activeTab) {
      setActiveTab(displayFolders[0].path);
    }
  }, [displayFolders, activeTab]);
  
  // Get files for the active tab
  const getActiveTabFiles = () => {
    if (!activeTab) return [];
    return allFiles
      .filter(file => file.path.startsWith(activeTab))
      .map(file => ({
        name: file.name,
        path: file.path,
        type: 'file' as const
      }));
  };
  
  const handleTabClick = (folder: NavigableFolderItem) => {
    setActiveTab(folder.path);
  };
  
  return (
    <div className="output-panel">
      <div className="output-panel__content">
        <FolderTabs
          folders={displayFolders}
          activeTab={activeTab}
          onTabClick={handleTabClick}
          showAddButton={showAddButton}
          onAddNew={onAddNew}
          title="To Review"
        />
        
        <div className="output-panel__actions">
          <ViewToggle currentView={viewMode} onViewChange={setViewMode} />
        </div>
        
        <div className="output-panel__files">
          <div className={`output-panel__files-container output-panel__files-container--${viewMode}`}>
            {getActiveTabFiles().map((file) => (
              <FileItem
                key={file.path}
                file={file}
                onAction={(action, file) => onFileClick(file)}
                viewMode={viewMode}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OutputPanel; 