import Button from '@/components/ui/Button/Button';
import { search, SearchHit } from '@/services/search.service';
import { MissingIndexError } from '@/types/errors';
import { ChatMessage, SpeechRecognition, VoiceInterfaceProps } from '@/types/voice-assistant.types';
import { Mi<PERSON>, MicOff, Send, ChevronLeft, ChevronRight } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import './VoiceInterface.scss';

interface ChatMessagesProps {
  messages: ChatMessage[];
}

const ChatMessages = ({ messages }: ChatMessagesProps) => {
  return (
    <div className="voice-interface__chat">
      {messages.map(msg => (
        <div key={msg.id} className={`voice-interface__message voice-interface__message--${msg.type}`}>
          <div className="voice-interface__message-content">{msg.text}</div>
          <div className="voice-interface__message-timestamp">
            {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      ))}
    </div>
  );
};

const VoiceInterface = ({ variant = 'default', currentProject = '', onExpandedChange }: VoiceInterfaceProps) => {
  const [isListening, setIsListening] = useState(false);
  const [message, setMessage] = useState('');
  const [transcript, setTranscript] = useState('');
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const [isSending, setIsSending] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isExpanded, setIsExpanded] = useState(true);
  const silenceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastTranscriptRef = useRef<string>('');

  useEffect(() => {
    if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();

      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      recognition.onresult = event => {
        const current = event.resultIndex;
        const transcript = event.results[current][0].transcript;
        setTranscript(transcript);
        lastTranscriptRef.current = transcript;

        if (silenceTimerRef.current) {
          clearTimeout(silenceTimerRef.current);
        }
        silenceTimerRef.current = setTimeout(() => {
          if (isListening && lastTranscriptRef.current === transcript) {
            stopListening();
          }
        }, 3000);
      };

      recognition.onerror = event => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
      };

      recognition.onend = () => {
        if (isListening) {
          recognition.start();
        }
      };

      setRecognition(recognition);
    }

    return () => {
      if (recognition) {
        recognition.stop();
      }
      if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
      }
    };
  }, []);

  const stopListening = () => {
    if (recognition) {
      recognition.stop();
      setMessage(prev => prev + (transcript ? ' ' + transcript : ''));
      setTranscript('');
      setIsListening(false);
    }
  };

  const toggleListening = () => {
    if (!recognition) {
      console.error('Speech recognition not supported');
      return;
    }

    if (isListening) {
      stopListening();
    } else {
      recognition.start();
      setIsListening(true);
    }
  };

  const handleSend = async () => {
    if (isListening) {
      stopListening();
    }

    const finalMessage = (message + (transcript ? ' ' + transcript : '')).trim();

    if (finalMessage) {
      setMessage('');
      setTranscript('');

      setIsSending(true);
      try {
        const userMessage: ChatMessage = {
          id: Date.now().toString(),
          text: finalMessage,
          type: 'user',
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, userMessage]);

        const data = await search({
          query: finalMessage,
          company: currentProject || 'default',
        });

        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          text: `Found ${data.total_hits} results. ${data.hits.length > 0 ? 'Here are the top results:' : 'No results found.'}`,
          type: 'assistant',
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, assistantMessage]);

        // If there are hits, add them as separate messages
        if (data.hits.length > 0) {
          data.hits.forEach((hit: SearchHit, index: number) => {
            const hitMessage: ChatMessage = {
              id: (Date.now() + index + 2).toString(),
              text: (
                <div className="voice-interface__search-result">
                  <div className="voice-interface__search-result-header">
                    <span className="voice-interface__document-name">{hit.document_name.split('/').pop()}</span>
                    <span className="voice-interface__score">{Math.round(hit.score * 100)}% match</span>
                  </div>
                  <div className="voice-interface__search-result-content">{hit.chunk_content}</div>
                </div>
              ),
              type: 'assistant',
              timestamp: new Date(),
            };
            setMessages(prev => [...prev, hitMessage]);
          });
        }
      } catch (error) {
        console.error('Error sending message:', error);
        let errorText = 'Sorry, there was an error processing your request.';
        if (error instanceof MissingIndexError) {
          errorText = 'Please run the Audit process first before attempting to search.';
        }
        const errorMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          text: errorText,
          type: 'assistant',
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, errorMessage]);
      } finally {
        setIsSending(false);
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const toggleExpanded = () => {
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);
    if (onExpandedChange) {
      onExpandedChange(newExpandedState);
    }
  };

  if (variant === 'panel') {
    return (
      <div className={`voice-interface voice-interface--panel ${!isExpanded ? 'voice-interface--collapsed' : 'voice-interface--expanded'}`}>
        <button 
          className="voice-interface__toggle"
          onClick={toggleExpanded}
          aria-label={isExpanded ? 'Collapse voice panel' : 'Expand voice panel'}
        >
          {isExpanded ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
        </button>
        
        {isExpanded && (
          <>
            <div className="voice-interface__header">
              <h2 className="voice-interface__title">AI Assistant</h2>
              {isListening && <div className="voice-interface__speaking-indicator">Listening...</div>}
            </div>

            <ChatMessages messages={messages} />

            <div className="voice-interface__controls">
              <Button
                variant={isListening ? 'secondary' : 'primary'}
                icon={isListening ? <MicOff size={20} /> : <Mic size={20} />}
                onClick={toggleListening}
                className="voice-interface__mic-button"
              >
                {isListening ? 'Stop' : 'Speak'}
              </Button>
            </div>

            <div className="voice-interface__input-area">
              <textarea
                className="voice-interface__input"
                value={isListening ? message + (transcript ? ' ' + transcript : '') : message}
                onChange={e => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type or speak your message..."
                rows={3}
              />
              <Button
                variant="primary"
                icon={<Send size={16} />}
                iconOnly
                onClick={handleSend}
                disabled={(!message.trim() && !transcript) || isSending}
                loading={isSending}
                className="voice-interface__send-button"
                aria-label="Send message"
              />
            </div>
          </>
        )}
      </div>
    );
  }

  return (
    <div className="voice-interface">
      <div className="voice-interface__header">
        <h2 className="voice-interface__title">AI Assistant</h2>
        {isListening && <div className="voice-interface__speaking-indicator">Listening...</div>}
      </div>

      <ChatMessages messages={messages} />

      <div className="voice-interface__controls">
        <Button
          variant={isListening ? 'secondary' : 'primary'}
          icon={isListening ? <MicOff size={20} /> : <Mic size={20} />}
          onClick={toggleListening}
          className="voice-interface__mic-button"
        >
          {isListening ? 'Stop' : 'Speak'}
        </Button>
      </div>

      <div className="voice-interface__input-area">
        <textarea
          className="voice-interface__input"
          value={isListening ? message + (transcript ? ' ' + transcript : '') : message}
          onChange={e => setMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Type or speak your message..."
          rows={3}
        />
        <Button
          variant="primary"
          icon={<Send size={16} />}
          iconOnly
          onClick={handleSend}
          disabled={(!message.trim() && !transcript) || isSending}
          loading={isSending}
          className="voice-interface__send-button"
          aria-label="Send message"
        />
      </div>
    </div>
  );
};

export default VoiceInterface;
