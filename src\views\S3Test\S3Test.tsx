import { useState } from 'react';
import {
  listFolders,
  listFiles,
  createFolder,
  uploadFile,
  deleteFile,
  deleteFolder,
} from '@/services/file-storage.service';

export const S3Test = () => {
  const [currentPath, setCurrentPath] = useState('');
  const [folders, setFolders] = useState<{ name: string; fullPath: string }[]>([]);
  const [files, setFiles] = useState<{ name: string; fullPath: string }[]>([]);
  const [newFolderName, setNewFolderName] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [status, setStatus] = useState<string>('');
  const [error, setError] = useState<string>('');

  const handleListFolders = async () => {
    try {
      setError('');
      setStatus('Loading folders...');
      const result = await listFolders(currentPath);
      setFolders(result);
      setStatus('Folders loaded successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(`Error loading folders: ${errorMessage}`);
      setStatus('Failed to load folders');
      console.error('List folders error:', error);
    }
  };

  const handleListFiles = async () => {
    try {
      setError('');
      setStatus('Loading files...');
      const result = await listFiles(currentPath);
      setFiles(result);
      setStatus('Files loaded successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(`Error loading files: ${errorMessage}`);
      setStatus('Failed to load files');
      console.error('List files error:', error);
    }
  };

  const handleCreateFolder = async () => {
    if (!newFolderName) return;
    try {
      setError('');
      setStatus('Creating folder...');
      const path = currentPath ? `${currentPath}/${newFolderName}` : newFolderName;
      await createFolder(path);
      setStatus('Folder created successfully');
      setNewFolderName('');
      handleListFolders();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(`Error creating folder: ${errorMessage}`);
      setStatus('Failed to create folder');
      console.error('Create folder error:', error);
    }
  };

  const handleFileUpload = async () => {
    if (!selectedFile) return;
    try {
      setError('');
      setStatus('Uploading file...');
      await uploadFile(currentPath, selectedFile);
      setStatus('File uploaded successfully');
      setSelectedFile(null);
      handleListFiles();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(`Error uploading file: ${errorMessage}`);
      setStatus('Failed to upload file');
      console.error('Upload file error:', error);
    }
  };

  const handleDeleteFile = async (file: { name: string; fullPath: string }) => {
    try {
      setError('');
      setStatus('Deleting file...');
      await deleteFile(file.fullPath);
      setStatus('File deleted successfully');
      handleListFiles();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(`Error deleting file: ${errorMessage}`);
      setStatus('Failed to delete file');
      console.error('Delete file error:', error);
    }
  };

  const handleDeleteFolder = async (folder: { name: string; fullPath: string }) => {
    try {
      setError('');
      setStatus('Deleting folder...');
      await deleteFolder(folder.fullPath);
      setStatus('Folder deleted successfully');
      handleListFolders();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(`Error deleting folder: ${errorMessage}`);
      setStatus('Failed to delete folder');
      console.error('Delete folder error:', error);
    }
  };

  return (
    <div className="p-4 max-w-4xl mx-auto font-['Poppins']">
      <h1 className="text-2xl font-bold mb-4">S3 Test Component</h1>

      <div className="mb-4">
        <label className="block mb-2">Current Path:</label>
        <input
          type="text"
          value={currentPath}
          onChange={e => setCurrentPath(e.target.value)}
          className="w-full p-2 border rounded font-['Poppins']"
          placeholder="Enter path (e.g., project1/2024/january)"
        />
      </div>

      <div className="flex gap-4 mb-4">
        <button
          onClick={handleListFolders}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 font-['Poppins']"
        >
          List Folders
        </button>
        <button
          onClick={handleListFiles}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 font-['Poppins']"
        >
          List Files
        </button>
      </div>

      <div className="mb-4">
        <label className="block mb-2">Create New Folder:</label>
        <div className="flex gap-2">
          <input
            type="text"
            value={newFolderName}
            onChange={e => setNewFolderName(e.target.value)}
            className="flex-1 p-2 border rounded font-['Poppins']"
            placeholder="Enter folder name"
          />
          <button
            onClick={handleCreateFolder}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 font-['Poppins']"
          >
            Create Folder
          </button>
        </div>
      </div>

      <div className="mb-4">
        <label className="block mb-2">Upload File:</label>
        <div className="flex gap-2">
          <input
            type="file"
            onChange={e => setSelectedFile(e.target.files?.[0] || null)}
            className="flex-1 p-2 border rounded font-['Poppins']"
          />
          <button
            onClick={handleFileUpload}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 font-['Poppins']"
          >
            Upload
          </button>
        </div>
      </div>

      <div className="mb-4">
        <h2 className="text-xl font-semibold mb-2">Folders:</h2>
        <ul className="list-disc pl-5">
          {folders.map(folder => (
            <li key={folder.fullPath} className="flex items-center gap-2">
              {folder.name}
              <button
                onClick={() => handleDeleteFolder(folder)}
                className="px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm font-['Poppins']"
              >
                Delete
              </button>
            </li>
          ))}
        </ul>
      </div>

      <div className="mb-4">
        <h2 className="text-xl font-semibold mb-2">Files:</h2>
        <ul className="list-disc pl-5">
          {files.map(file => (
            <li key={file.fullPath} className="flex items-center gap-2">
              {file.name}
              <button
                onClick={() => handleDeleteFile(file)}
                className="px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm font-['Poppins']"
              >
                Delete
              </button>
            </li>
          ))}
        </ul>
      </div>

      <div className="mt-4 p-4 bg-gray-100 rounded">
        <h2 className="text-lg font-semibold mb-2">Status:</h2>
        <p className="mb-2">{status}</p>
        {error && (
          <div className="mt-2 p-2 bg-red-100 text-red-700 rounded">
            <h3 className="font-semibold">Error:</h3>
            <p>{error}</p>
          </div>
        )}
      </div>
    </div>
  );
};
