import React, { useEffect } from 'react';
import { useFileStorage } from '@/hooks/useFileStorage';
import { Folder } from '@/types/file-storage.types';

interface FolderTreeProps {
  currentPath: string;
  onFolderClick: (path: string) => void;
}

const FolderTree: React.FC<FolderTreeProps> = ({ currentPath, onFolderClick }) => {
  const { folders, loadFolders } = useFileStorage();

  useEffect(() => {
    loadFolders(currentPath);
  }, [currentPath, loadFolders]);

  return (
    <div className="p-4">
      <h2 className="text-lg font-semibold mb-2">Folders</h2>
      <ul>
        {folders.map((folder: Folder) => (
          <li key={folder.path} className="mb-1">
            <button onClick={() => onFolderClick(folder.path)} className="text-blue-600 hover:underline">
              {folder.name}
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default FolderTree;
