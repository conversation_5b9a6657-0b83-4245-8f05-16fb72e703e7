@use "sass:color";
@use "@/styles/variables" as *;
@use "../../../styles/variables" as vars;

.folder-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: vars.$spacing-sm vars.$spacing-md;
  border-radius: vars.$border-radius-sm;
  background-color: $primary-color;
  box-shadow: $shadow-sm;
  cursor: pointer;
  transition: transform $transition-fast, box-shadow $transition-fast;
  font-family: 'Poppins', system-ui, sans-serif;
  border: 1px solid $border-color;
  color: vars.$text-color;
  text-decoration: none;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-md;
    background-color: vars.$color-background-hover;
  }

  &__icon {
    margin-bottom: $spacing-sm;
    color: vars.$color-gray-500;
    flex-shrink: 0;
  }

  &__name {
    font-size: vars.$font-size-base;
    font-weight: vars.$font-weight-medium;
    color: $color-text;
    text-align: center;
    font-family: 'Poppins', system-ui, sans-serif;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__actions {
    display: flex;
    gap: vars.$spacing-xs;
    opacity: 0;
    transition: opacity vars.$transition-fast;
  }

  &:hover &__actions {
    opacity: 1;
  }

  &__action {
    background: none;
    border: none;
    cursor: pointer;
    padding: vars.$spacing-xs;
    border-radius: vars.$border-radius-sm;
    color: vars.$color-gray-500;
    transition: all vars.$transition-fast;

    &:hover {
      background-color: vars.$color-gray-100;
      color: vars.$text-color;
    }
  }
} 