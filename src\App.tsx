import ProtectedRoute from '@/components/ProtectedRoute';
import { TooltipProvider } from '@/components/ui/tooltip';
import { AuthProvider } from '@/contexts/AuthContext';
import { BrowserRouter, Route, Routes } from 'react-router-dom';

import Dashboard from '@/views/Dashboard/Dashboard';
import Index from '@/views/Index/Index';
import Login from '@/views/Login/Login';
import NotFound from '@/views/NotFound/NotFound';
import { S3Test } from '@/views/S3Test/S3Test';

import '@/styles/tailwind.scss'; // Tailwind base styles and theme configuration

const App = () => (
  <BrowserRouter>
    <AuthProvider>
      <TooltipProvider>
        <div className="relative min-h-screen w-full flex flex-col">
          <div className="flex-1 min-w-0">
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/login" element={<Login />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/s3-test"
                element={
                  <ProtectedRoute>
                    <S3Test />
                  </ProtectedRoute>
                }
              />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </div>
        </div>
      </TooltipProvider>
    </AuthProvider>
  </BrowserRouter>
);

export default App;
