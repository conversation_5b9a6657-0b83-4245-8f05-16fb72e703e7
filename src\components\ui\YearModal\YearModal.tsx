import React, { useState } from 'react';
import Button from '../Button/Button';
import Modal from '../Modal/Modal';
import './YearModal.scss';

interface YearModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateYear: (fiscalYear: string) => Promise<void>;
  existingFolders: Array<{ name: string; fullPath: string; type: 'folder' | 'file' }>;
}

// Generate fiscal years (FY22, FY23, etc.)
const getFiscalYears = () => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let i = 0; i < 32; i++) {
    const year = currentYear - 30 + i;
    years.push(`FY${year.toString().slice(-2)}`);
  }
  return years.reverse();
};

const FISCAL_YEARS = getFiscalYears();

const YearModal: React.FC<YearModalProps> = ({ isOpen, onClose, onCreateYear, existingFolders }) => {
  const [fiscalYear, setFiscalYear] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleClose = () => {
    setError(null);
    onClose();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!fiscalYear) {
      setError('Please select a fiscal year');
      return;
    }

    // Check for duplicate fiscal year
    if (existingFolders.some(folder => folder.name === fiscalYear)) {
      setError('This fiscal year already exists in the project');
      return;
    }

    try {
      setIsLoading(true);
      await onCreateYear(fiscalYear);
      setFiscalYear('');
      handleClose();
    } catch (err) {
      setError('Failed to create fiscal year. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Add Fiscal Year">
      <form onSubmit={handleSubmit} className="year-modal__form">
        <div className="year-modal__input-group">
          <label htmlFor="fiscalYear" className="year-modal__label">
            Fiscal Year
          </label>
          <select
            id="fiscalYear"
            value={fiscalYear}
            onChange={e => setFiscalYear(e.target.value)}
            className="year-modal__input"
            disabled={isLoading}
          >
            <option value="">Select a fiscal year</option>
            {FISCAL_YEARS.map(year => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>
          {error && <div className="year-modal__error">{error}</div>}
        </div>
        <div className="year-modal__actions">
          <Button type="button" variant="secondary" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button type="submit" variant="primary" disabled={isLoading}>
            {isLoading ? 'Creating...' : 'Add Fiscal Year'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default YearModal;
