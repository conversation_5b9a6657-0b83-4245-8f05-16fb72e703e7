import Button from '@/components/ui/Button/Button';
import React, { useState } from 'react';
import './ProjectModal.scss';

interface ProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateProject: (projectName: string, year: string) => Promise<void>;
  existingFolders: Array<{ name: string; fullPath: string; type: 'folder' | 'file' }>;
}

// Generate fiscal years (FY22, FY23, etc.)
const getFiscalYears = () => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let i = 0; i < 32; i++) {
    const year = currentYear - 30 + i;
    years.push(`FY${year.toString().slice(-2)}`);
  }
  return years.reverse();
};

const FISCAL_YEARS = getFiscalYears();

const ProjectModal: React.FC<ProjectModalProps> = ({ isOpen, onClose, onCreateProject, existingFolders }) => {
  const [projectName, setProjectName] = useState('');
  const [year, setYear] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleClose = () => {
    setError(null);
    onClose();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!projectName.trim()) {
      setError('Project name cannot be empty');
      return;
    }

    if (!year) {
      setError('Please select a fiscal year');
      return;
    }

    // Check for duplicate project name
    if (existingFolders.some(folder => folder.name === projectName.trim())) {
      setError('A project with this name already exists');
      return;
    }

    setIsLoading(true);

    try {
      await onCreateProject(projectName.trim(), year);
      setProjectName('');
      setYear('');
      handleClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create project');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="project-modal-overlay" onClick={handleClose}>
      <div className="project-modal" onClick={e => e.stopPropagation()}>
        <h2 className="project-modal__title">Create New Project</h2>
        <form onSubmit={handleSubmit} className="project-modal__form">
          <div className="project-modal__input-group">
            <label htmlFor="projectName" className="project-modal__label">
              Project Name
            </label>
            <input
              id="projectName"
              type="text"
              value={projectName}
              onChange={e => setProjectName(e.target.value)}
              className="project-modal__input"
              placeholder="Enter project name"
              disabled={isLoading}
            />
          </div>
          <div className="project-modal__input-group">
            <label htmlFor="year" className="project-modal__label">
              Fiscal Year
            </label>
            <select
              id="year"
              value={year}
              onChange={e => setYear(e.target.value)}
              className="project-modal__input"
              disabled={isLoading}
            >
              <option value="">Select a fiscal year</option>
              {FISCAL_YEARS.map(year => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
          </div>
          {error && <p className="project-modal__error">{error}</p>}
          <div className="project-modal__actions">
            <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" variant="primary" disabled={isLoading}>
              {isLoading ? 'Creating...' : 'Create Project'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProjectModal;
