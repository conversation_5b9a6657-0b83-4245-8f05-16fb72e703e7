import { getCredentialsProvider } from '@/services/aws-credentials.service';
import { GetObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import React, { useEffect, useState } from 'react';
import * as XLSX from 'xlsx';
import Modal from '../Modal/Modal';
import './FilePreviewModal.scss';

interface FilePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  file: {
    name: string;
    path: string;
    type: string;
  } | null;
}

interface ExcelSheet {
  name: string;
  htmlContent: string;
}

interface ExcelData {
  sheets: ExcelSheet[];
  activeSheet: number;
}

const BUCKET_NAME = import.meta.env.VITE_AWS_S3_BUCKET_NAME;

const getS3Client = async () => {
  const credentialsProvider = await getCredentialsProvider();
  return new S3Client({
    region: import.meta.env.VITE_AWS_REGION || 'us-east-1',
    credentials: credentialsProvider,
  });
};

const FilePreviewModal: React.FC<FilePreviewModalProps> = ({ isOpen, onClose, file }) => {
  const [excelData, setExcelData] = useState<ExcelData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [signedUrl, setSignedUrl] = useState<string | null>(null);

  const getSignedS3Url = async (path: string) => {
    try {
      const s3Client = await getS3Client();
      const command = new GetObjectCommand({
        Bucket: BUCKET_NAME,
        Key: path,
      });

      const url = await getSignedUrl(s3Client, command, { expiresIn: 900 });
      return url;
    } catch (err) {
      console.error('Error generating signed URL:', err);
      throw err;
    }
  };

  useEffect(() => {
    if (file && isOpen) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (['xlsx', 'xls', 'csv'].includes(fileExtension || '')) {
        loadExcelFile();
      } else {
        setExcelData(null); // Clear any previous excel data
        setSignedUrl(null); // Clear previous signed URL if not an excel file
        getSignedS3Url(file.path)
          .then(url => setSignedUrl(url))
          .catch(err => setError('Failed to generate file preview URL'));
      }
    } else if (!isOpen) {
      // Clear states when modal is closed
      setExcelData(null);
      setSignedUrl(null);
      setError(null);
      setIsLoading(false);
    }
  }, [file, isOpen]);

  const loadExcelFile = async () => {
    if (!file) return;

    setIsLoading(true);
    setError(null);
    setExcelData(null);

    try {
      const url = await getSignedS3Url(file.path);
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch file: ${response.status} ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });

      if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
        throw new Error('No sheet names found in the workbook');
      }

      if (!workbook.Sheets) {
        throw new Error('workbook.Sheets is undefined after parsing.');
      }

      const sheets = workbook.SheetNames.map(name => {
        const worksheet = workbook.Sheets[name];
        if (!worksheet) {
          return { name, htmlContent: '<p>Sheet data not available.</p>' };
        }
        const htmlContent = XLSX.utils.sheet_to_html(worksheet);
        return {
          name,
          htmlContent,
        };
      });

      const newExcelData = {
        sheets,
        activeSheet: 0,
      };
      setExcelData(newExcelData);
    } catch (err) {
      console.error('[ExcelPreview] Error loading Excel file:', err);
      setError(err instanceof Error ? err.message : 'Failed to load Excel file. Please try downloading it instead.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSheetChange = (index: number) => {
    if (excelData) {
      setExcelData({
        ...excelData,
        activeSheet: index,
      });
    }
  };

  const getFilePreview = () => {
    if (!file) {
      return null;
    }

    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    if (['jpg', 'jpeg', 'png'].includes(fileExtension || '')) {
      if (!signedUrl) {
        return <div className="file-preview-modal__loading">Loading image...</div>;
      }
      return (
        <div className="file-preview-modal__image-container">
          <img src={signedUrl} alt={file.name} className="file-preview-modal__image" />
        </div>
      );
    }

    if (fileExtension === 'pdf') {
      if (!signedUrl) {
        return <div className="file-preview-modal__loading">Loading PDF...</div>;
      }
      return (
        <div className="file-preview-modal__pdf-container">
          <iframe src={signedUrl} title={file.name} className="file-preview-modal__pdf" />
        </div>
      );
    }

    if (['xlsx', 'xls', 'csv'].includes(fileExtension || '')) {
      if (isLoading) {
        return <div className="file-preview-modal__loading">Loading Excel file...</div>;
      }

      if (error) {
        return <div className="file-preview-modal__error">{error}</div>;
      }

      if (!excelData || excelData.sheets.length === 0) {
        return <div className="file-preview-modal__message">No data available in the Excel file.</div>;
      }

      const currentSheet = excelData.sheets[excelData.activeSheet];

      if (!currentSheet || !currentSheet.htmlContent) {
        return <div className="file-preview-modal__message">Sheet data is not available.</div>;
      }

      return (
        <div className="file-preview-modal__excel-container">
          {excelData.sheets.length > 1 && (
            <div className="file-preview-modal__excel-tabs">
              {excelData.sheets.map((sheet, index) => (
                <button
                  key={sheet.name}
                  className={`file-preview-modal__excel-tab ${
                    index === excelData.activeSheet ? 'file-preview-modal__excel-tab--active' : ''
                  }`}
                  onClick={() => handleSheetChange(index)}
                >
                  {sheet.name}
                </button>
              ))}
            </div>
          )}
          <div
            className="file-preview-modal__excel-html-wrapper"
            dangerouslySetInnerHTML={{ __html: currentSheet.htmlContent }}
          />
        </div>
      );
    }

    return <div className="file-preview-modal__message">Preview not available for this file type.</div>;
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={file?.name || 'File Preview'}>
      <div className="file-preview-modal">{getFilePreview()}</div>
    </Modal>
  );
};

export default FilePreviewModal;
