@use '../../../styles/variables' as *;

.organization-demo {
  padding: $spacing-lg;
  max-width: 600px;
  margin: 0 auto;
  font-family: $font-family-base;

  &__title {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $text-color-primary;
    margin-bottom: $spacing-lg;
    text-align: center;
  }

  &__section {
    margin-bottom: $spacing-lg;
    padding: $spacing-md;
    border: 1px solid $border-color;
    border-radius: $border-radius-md;
    background-color: $background-color-white;
  }

  &__section-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-color-primary;
    margin-bottom: $spacing-md;
  }

  &__current {
    p {
      margin-bottom: $spacing-xs;
      font-size: $font-size-sm;
      color: $text-color-primary;

      strong {
        color: $text-color-secondary;
        font-weight: $font-weight-medium;
      }
    }
  }

  &__no-org,
  &__no-orgs {
    color: $text-color-secondary;
    font-style: italic;
    font-size: $font-size-sm;
  }

  &__list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  &__list-item {
    padding: $spacing-sm;
    border-bottom: 1px solid $border-color;
    font-size: $font-size-sm;
    color: $text-color-primary;

    &:last-child {
      border-bottom: none;
    }

    strong {
      color: $primary-color;
      font-weight: $font-weight-medium;
    }
  }

  &__error {
    color: $error-color;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    padding: $spacing-sm;
    background-color: rgba($error-color, 0.1);
    border-radius: $border-radius-sm;
  }
} 