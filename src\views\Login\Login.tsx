import { Card } from '@/components/ui/Card/Card';
import LoginCard from '@/components/ui/LoginCard/LoginCard';
import { useAuth } from '@/contexts/AuthContext';
import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import './Login.scss';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated) {
      // Redirect to the originally requested page or dashboard
      const from = (location.state as { from?: { pathname: string } })?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  return (
    <div className="login-page">
      <Card>
        <LoginCard />
      </Card>
    </div>
  );
};

export default Login;
