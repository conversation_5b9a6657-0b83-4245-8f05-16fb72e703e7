import React from 'react';
import { Folder } from 'lucide-react';
import { NavigableFolderItem } from '@/types';
import './FolderItem.scss';

interface FolderItemProps {
  folder: NavigableFolderItem;
  onClick: () => void;
}

const FolderItem: React.FC<FolderItemProps> = ({ folder, onClick }) => {
  return (
    <div className="folder-item" onClick={onClick}>
      <div className="folder-item__icon">
        <Folder size={24} />
      </div>
      <div className="folder-item__name">{folder.name}</div>
    </div>
  );
};

export default FolderItem;
