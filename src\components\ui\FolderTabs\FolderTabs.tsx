import React, { useState, useEffect } from 'react';
import { Plus } from 'lucide-react';
import { NavigableFolderItem } from '@/types';
import { useFileStorage } from '@/hooks/useFileStorage';
import './FolderTabs.scss';

interface FolderTabsProps {
  folders?: NavigableFolderItem[]; // Original prop for direct folder list
  dataSource?: string; // e.g., "Outputs", "General Ledger", etc.
  basePath?: string; // The base path to append dataSource to
  activeTab?: string;
  onTabClick: (folder: NavigableFolderItem) => void;
  onAddNew?: () => void;
  showAddButton?: boolean;
  title?: string; // Optional title for the panel
}

const FolderTabs: React.FC<FolderTabsProps> = ({
  folders: directFolders,
  dataSource,
  basePath = '',
  activeTab,
  onTabClick,
  onAddNew,
  showAddButton = false,
  title,
}) => {
  const [hoveredTab, setHoveredTab] = useState<string | null>(null);
  const [folders, setFolders] = useState<NavigableFolderItem[]>([]);
  const { folders: allFolders, files: allFiles } = useFileStorage();

  // Calculate the full path for the data source
  const dataSourcePath = dataSource ? `${basePath}/${dataSource}`.replace(/\/+/g, '/') : basePath;

  // Filter folders for the data source or use direct folders
  useEffect(() => {
    if (directFolders) {
      // Use direct folders if provided (original behavior)
      setFolders(directFolders);
    } else if (dataSourcePath) {
      // Use data source filtering (new behavior)
      const filteredFolders: NavigableFolderItem[] = allFolders
        .filter(folder => 
          folder.path.startsWith(dataSourcePath) && 
          folder.path !== dataSourcePath &&
          folder.path.split('/').length === dataSourcePath.split('/').length + 1
        )
        .map(folder => ({
          name: folder.name,
          path: folder.path,
          type: 'folder' as const
        }));
      setFolders(filteredFolders);
    }
  }, [directFolders, allFolders, dataSourcePath]);

  const handleTabClick = (folder: NavigableFolderItem) => {
    onTabClick(folder);
  };

  const handleAddClick = () => {
    onAddNew?.();
  };

  return (
    <div className="folder-tabs">
      {title && (
        <div className="folder-tabs__title">{title}</div>
      )}
      <div className="folder-tabs__container">
        {folders.map((folder) => (
          <div
            key={folder.path}
            className={`folder-tabs__tab ${
              activeTab === folder.path ? 'folder-tabs__tab--active' : ''
            }`}
            onClick={() => handleTabClick(folder)}
            onMouseEnter={() => setHoveredTab(folder.path)}
            onMouseLeave={() => setHoveredTab(null)}
          >
            <div className="folder-tabs__tab-content">
              <span className="folder-tabs__tab-name">{folder.name}</span>
            </div>
          </div>
        ))}
        {showAddButton && onAddNew && (
          <button
            className="folder-tabs__add-button"
            onClick={handleAddClick}
            aria-label="Add new tab"
          >
            <Plus size={16} />
          </button>
        )}
      </div>
    </div>
  );
};

export default FolderTabs; 