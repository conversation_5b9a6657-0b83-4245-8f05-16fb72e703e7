import Button from '@/components/ui/Button/Button';
import { useAuth } from '@/contexts/AuthContext';
import { LoginCredentials } from '@/services/authentication.service';
import React, { useEffect, useState } from 'react';
import './LoginCard.scss';

interface LoginCardProps {
  onLoginSuccess?: () => void;
}

const LoginCard: React.FC<LoginCardProps> = ({ onLoginSuccess }) => {
  const [credentials, setCredentials] = useState<LoginCredentials>({
    email: '',
    password: '',
  });
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const { login, completeNewPassword, isAuthenticated, isLoading, error, requiresNewPassword } = useAuth();

  useEffect(() => {
    if (isAuthenticated) {
      onLoginSuccess?.();
    }
  }, [isAuthenticated, onLoginSuccess]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCredentials(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewPassword(e.target.value);
  };

  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConfirmPassword(e.target.value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await login(credentials);
  };

  const handleNewPasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newPassword !== confirmPassword) {
      // You might want to add this to the AuthContext error state
      console.error('Passwords do not match');
      return;
    }
    await completeNewPassword(credentials.email, newPassword);
  };

  if (requiresNewPassword) {
    return (
      <>
        <div className="login-card__title">
          <div>Set New Password</div>
          <div>Please choose a new password for your account.</div>
        </div>
        <form onSubmit={handleNewPasswordSubmit} className="login-card__form">
          <input
            type="password"
            value={newPassword}
            onChange={handleNewPasswordChange}
            placeholder="New Password"
            className="login-card__input"
            required
            minLength={8}
          />
          <input
            type="password"
            value={confirmPassword}
            onChange={handleConfirmPasswordChange}
            placeholder="Confirm New Password"
            className="login-card__input"
            required
            minLength={8}
          />
          {error && <p className="login-card__error">{error}</p>}
          <Button type="submit" variant="primary" disabled={isLoading} className="login-card__button">
            {isLoading ? 'Setting Password...' : 'Set New Password'}
          </Button>
        </form>
      </>
    );
  }

  return (
    <>
      <div className="login-card__title">
        <div>Welcome back!</div>
        <div>Let's get to work.</div>
      </div>
      <form onSubmit={handleSubmit} className="login-card__form">
        <input
          type="email"
          name="email"
          value={credentials.email}
          onChange={handleInputChange}
          placeholder="Email"
          className="login-card__input"
          required
        />
        <input
          type="password"
          name="password"
          value={credentials.password}
          onChange={handleInputChange}
          placeholder="Password"
          className="login-card__input"
          required
        />
        {error && <p className="login-card__error">{error}</p>}
        <Button type="submit" variant="primary" disabled={isLoading} className="login-card__button">
          {isLoading ? 'Logging in...' : 'Login'}
        </Button>
      </form>
    </>
  );
};

export default LoginCard;
