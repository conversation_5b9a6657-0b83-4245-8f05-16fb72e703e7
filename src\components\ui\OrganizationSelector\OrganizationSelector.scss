@use '../../../styles/variables' as *;

.organization-selector {
  position: relative;
  width: 100%;
  font-family: $font-family-base;

  &__loading,
  &__error,
  &__empty {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    color: $text-color-secondary;
    background-color: $background-color-light;
  }

  &__error {
    color: $error-color;
    background-color: rgba($error-color, 0.1);
  }

  &__trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid $border-color;
    border-radius: 6px;
    background-color: $background-color-white;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: $primary-color;
      background-color: rgba($primary-color, 0.05);
    }

    &.open {
      border-color: $primary-color;
      box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
    }
  }

  &__current {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
  }

  &__icon {
    color: $primary-color;
    flex-shrink: 0;
  }

  &__name {
    font-size: 14px;
    font-weight: 500;
    color: $text-color-primary;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__arrow {
    color: $text-color-secondary;
    transition: transform 0.2s ease;
    flex-shrink: 0;
  }

  &__trigger.open &__arrow {
    transform: rotate(180deg);
  }

  &__dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 4px;
    background-color: $background-color-white;
    border: 1px solid $border-color;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
  }

  &__dropdown-header {
    padding: 8px 12px;
    border-bottom: 1px solid $border-color;
    background-color: $background-color-light;
  }

  &__dropdown-title {
    font-size: 12px;
    font-weight: 600;
    color: $text-color-secondary;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__dropdown-list {
    padding: 4px 0;
  }

  &__option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba($primary-color, 0.1);
    }

    &.active {
      background-color: rgba($primary-color, 0.15);
      color: $primary-color;
    }
  }

  &__option-icon {
    color: $text-color-secondary;
    flex-shrink: 0;
  }

  &__option.active &__option-icon {
    color: $primary-color;
  }

  &__option-name {
    font-size: 14px;
    font-weight: 400;
    color: $text-color-primary;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__option.active &__option-name {
    color: $primary-color;
    font-weight: 500;
  }
} 