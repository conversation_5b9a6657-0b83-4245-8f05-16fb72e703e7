---
description: 
globs: 
alwaysApply: true
---
# Code Quality

- Prioritize clean, readable, and maintainable code over feature quantity.
- Avoid code duplication by abstracting repeated structures.
- Avoid unnecessary dependencies and keep the bundle size lean.
- Do not use `any`. Always define or infer types.
- Imports must always use '@' prefix when possible over relative paths.
- Exercise good separation of concerns to avoid huge components.
- Any helper logic should be outsite of the components and in functions inside a `utils/` folder
