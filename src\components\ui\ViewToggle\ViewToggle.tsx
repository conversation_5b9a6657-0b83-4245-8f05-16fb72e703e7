import React from 'react';
import { Grid, List } from 'lucide-react';
import Button from '@/components/ui/Button/Button';
import './ViewToggle.scss';

export type ViewMode = 'grid' | 'list';

interface ViewToggleProps {
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
}

const ViewToggle: React.FC<ViewToggleProps> = ({ currentView, onViewChange }) => {
  return (
    <div className="view-toggle">
      <button
        className={`view-toggle__button ${currentView === 'grid' ? 'view-toggle__button--active' : ''}`}
        onClick={() => onViewChange('grid')}
        aria-label="Grid view"
      >
        <Grid size={14} />
      </button>
      <button
        className={`view-toggle__button ${currentView === 'list' ? 'view-toggle__button--active' : ''}`}
        onClick={() => onViewChange('list')}
        aria-label="List view"
      >
        <List size={14} />
      </button>
    </div>
  );
};

export default ViewToggle; 