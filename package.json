{"name": "vite_react_shadcn_ts", "private": true, "version": "0.1.3", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "start": "node src/server.js"}, "dependencies": {"@aws-sdk/client-cognito-identity": "^3.823.0", "@aws-sdk/client-dynamodb": "^3.0.0", "@aws-sdk/client-s3": "^3.802.0", "@aws-sdk/client-textract": "^3.0.0", "@aws-sdk/credential-provider-cognito-identity": "^3.823.0", "@aws-sdk/s3-request-presigner": "^3.802.0", "@aws-sdk/util-dynamodb": "^3.0.0", "@radix-ui/react-dialog": "^1.0.0", "@radix-ui/react-scroll-area": "^1.0.0", "@radix-ui/react-separator": "^1.0.0", "@radix-ui/react-tooltip": "^1.1.4", "@tailwindcss/typography": "^0.5.15", "@types/xlsx": "^0.0.35", "@vitejs/plugin-react-swc": "^3.5.0", "amazon-cognito-identity-js": "^6.3.15", "autoprefixer": "^10.4.20", "clsx": "^2.1.1", "express": "^4.18.2", "lucide-react": "^0.462.0", "pdfjs-dist": "^3.4.120", "postcss": "^8.4.47", "react": "^18.3.1", "react-dnd": "^16.0.0", "react-dnd-html5-backend": "^16.0.0", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-pdf": "^7.7.0", "react-router-dom": "^6.26.2", "sass": "^1.87.0", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.11", "tailwindcss-animate": "^1.0.7", "vite": "^5.4.1", "vite-plugin-svgr": "^4.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.26.0", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.9", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.9.0", "prettier": "^3.5.3", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1"}}