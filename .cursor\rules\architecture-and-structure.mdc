---
description: 
globs: 
alwaysApply: true
---
# Architecture & Structure

- Always follow the project folder structure defined in the [README.md](mdc:README.md):
  - ui components → `src/components/ui/ComponentName/`
  - layout components → `src/components/layout/`
  - views → `src/views/`
  - services → `src/services/`
  - hooks → `src/hooks/`
  - types → `src/types/`
  - styles → `src/styles/`
- Do not place reusable components directly inside `views/`.