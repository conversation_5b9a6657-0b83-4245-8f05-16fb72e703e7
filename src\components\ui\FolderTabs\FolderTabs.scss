@use 'sass:color';
@use '../../../styles/variables' as vars;

.folder-tabs {
  width: 100%;
  background-color: vars.$primary-color;
  border-bottom: 1px solid vars.$primary-btn-color;

  &__title {
    font-family: vars.$font-family-sans-serif;
    font-size: vars.$font-size-base;
    font-weight: vars.$font-weight-normal;
    color: vars.$text-color;
    margin: vars.$spacing-lg 0;
  }

  &__container {
    display: flex;
    align-items: flex-end;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: thin;
    scrollbar-color: vars.$border-color transparent;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: vars.$border-color;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: color.adjust(vars.$border-color, $lightness: -10%);
    }
  }

  &__tab {
    display: flex;
    align-items: center;
    min-width: fit-content;
    max-width: none;
    height: 36px;
    background-color: vars.$color-gray-200;
    border: 1px solid vars.$color-gray-500;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    flex-shrink: 0;
    opacity: 0.5;
    margin-right: vars.$spacing-xs;
    padding: 0 1rem;
    white-space: nowrap;

    &:hover {
      background-color: vars.$color-gray-300;
      border-color: vars.$color-gray-500;
      opacity: 0.8;
    }

    &--active {
      background-color: vars.$primary-color;
      border-color: vars.$primary-btn-color;
      border-bottom: 1px solid vars.$primary-color;
      z-index: 2;
      margin-bottom: -1px;
      opacity: 1;
      height: 40px;

      &:hover {
        background-color: vars.$primary-color;
        opacity: 1;
      }
    }

    &:first-child {
      margin-left: vars.$spacing-sm;
    }
  }

  &__tab-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: vars.$spacing-xs;
  }

  &__tab-name {
    font-size: vars.$font-size-sm;
    font-weight: vars.$font-weight-normal;
    color: vars.$color-gray-500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;

    .folder-tabs__tab--active & {
      font-weight: vars.$font-weight-medium;
      color: vars.$text-color;
    }
  }

  &__add-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: transparent;
    border: 1px dashed vars.$primary-btn-color;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    cursor: pointer;
    color: vars.$color-gray-500;
    transition: all 0.2s ease;
    margin-left: vars.$spacing-xs;
    flex-shrink: 0;

    &:hover {
      background-color: color.adjust(vars.$primary-color, $lightness: -2%);
      border-color: vars.$color-gray-300;
      color: vars.$color-gray-600;
    }

    &:active {
      transform: scale(0.95);
    }
  }
} 