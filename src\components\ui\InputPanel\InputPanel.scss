@use '../../../styles/variables' as vars;

.input-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;

  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow-y: auto;
  }

  &__actions {
    padding: vars.$spacing-md vars.$spacing-xs;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__files {
    padding: vars.$spacing-xs;
    margin-top: 0;

    &--grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
      gap: vars.$spacing-md;
    }

    &--list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1px;
      max-height: 100%;
      overflow-y: auto;
      
      // Limit to 3 columns maximum using container queries
      container-type: inline-size;
      
      @container (min-width: 900px) {
        grid-template-columns: repeat(2, 1fr);
      }
      
      @container (min-width: 1400px) {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }

  &__loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: vars.$spacing-xl;
    color: vars.$color-gray-500;

    &-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid vars.$border-color;
      border-top-color: vars.$primary-btn-color;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: vars.$spacing-md;
    }

    &-text {
      font-size: vars.$font-size-base;
    }
  }

  &__empty-state {
    text-align: center;
    padding: vars.$spacing-xl;
    color: vars.$color-gray-500;

    .input-panel__empty-title {
      font-size: vars.$font-size-base;
      font-weight: vars.$font-weight-medium;
      margin-bottom: vars.$spacing-xs;
    }

    .input-panel__empty-desc {
      font-size: vars.$font-size-sm;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
} 