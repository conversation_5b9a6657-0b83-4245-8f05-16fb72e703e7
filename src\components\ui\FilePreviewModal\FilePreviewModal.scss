@use "../../../styles/variables" as vars;

.file-preview-modal {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: vars.$font-family-sans-serif;

  &__header {
    padding: vars.$spacing-lg;
    border-bottom: 1px solid vars.$border-color;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__title {
    font-size: vars.$font-size-lg;
    font-weight: vars.$font-weight-semibold;
    color: vars.$text-color;
    margin: 0;
  }

  &__close {
    background: none;
    border: none;
    cursor: pointer;
    padding: vars.$spacing-xs;
    border-radius: vars.$border-radius-sm;
    color: vars.$color-gray-500;
    transition: all vars.$transition-fast;

    &:hover {
      background-color: vars.$color-background-hover;
      color: vars.$text-color;
    }
  }

  &__content {
    padding: vars.$spacing-lg;
    max-height: 70vh;
    overflow-y: auto;
  }

  &__preview {
    width: 100%;
    height: auto;
    border-radius: vars.$border-radius-sm;
    margin-bottom: vars.$spacing-md;
  }

  &__info {
    display: flex;
    flex-direction: column;
    gap: vars.$spacing-sm;
  }

  &__info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: vars.$spacing-sm 0;
    border-bottom: 1px solid vars.$border-color;

    &:last-child {
      border-bottom: none;
    }
  }

  &__info-label {
    font-size: vars.$font-size-sm;
    font-weight: vars.$font-weight-semibold;
    color: vars.$text-color;
  }

  &__info-value {
    font-size: vars.$font-size-sm;
    color: vars.$color-gray-500;
  }

  &__actions {
    padding: vars.$spacing-lg;
    border-top: 1px solid vars.$border-color;
    display: flex;
    gap: vars.$spacing-sm;
    justify-content: flex-end;
  }

  &__image-container,
  &__pdf-container,
  &__excel-container {
    flex: 1;
    display: flex;
    background-color: vars.$color-gray-100;
    border-radius: vars.$border-radius;
    overflow: hidden;
    min-height: 0;
  }

  &__image-container,
  &__pdf-container {
    align-items: center;
    justify-content: center;
  }

  &__excel-container {
    flex-direction: column;
    padding: vars.$spacing-sm;
    background-color: vars.$primary-color;
  }

  &__image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  &__pdf {
    width: 100%;
    height: 100%;
    border: none;
  }

  &__message,
  &__loading,
  &__error {
    color: vars.$color-gray-600;
    font-size: vars.$font-size-base;
    text-align: center;
    padding: vars.$spacing-xl;
    margin: auto;
  }

  &__error {
    color: vars.$color-error;
  }

  &__excel-tabs {
    display: flex;
    margin-bottom: vars.$spacing-sm;
    border-bottom: 1px solid vars.$border-color;
    flex-shrink: 0;
  }

  &__excel-tab {
    padding: vars.$spacing-sm vars.$spacing-md;
    cursor: pointer;
    background-color: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    color: vars.$color-gray-600;
    font-family: vars.$font-family-sans-serif;
    font-size: vars.$font-size-sm;
    margin-right: vars.$spacing-xs;
    transition: color vars.$transition-fast, border-color vars.$transition-fast;

    &:hover {
      color: vars.$text-color;
    }

    &--active {
      color: vars.$primary-btn-color;
      border-bottom-color: vars.$primary-btn-color;
      font-weight: vars.$font-weight-semibold;
    }
  }

  &__excel-html-wrapper {
    flex: 1;
    overflow: auto;
    background-color: vars.$primary-color;
    border: 1px solid vars.$border-color;
    border-radius: vars.$border-radius-sm;
    padding: vars.$spacing-md;

    table {
      width: 100%;
      border-collapse: collapse;
      font-size: vars.$font-size-xs;
      font-family: vars.$font-family-sans-serif;
      background-color: vars.$primary-color;
      color: vars.$text-color;
      
      th, td {
        border: 1px solid vars.$border-color;
        padding: vars.$spacing-xs vars.$spacing-sm;
        text-align: left;
        vertical-align: top;
      }

      th {
        background-color: vars.$color-gray-100;
        font-weight: vars.$font-weight-semibold;
        position: sticky;
        top: 0;
        z-index: 1;
      }
    }
  }
} 