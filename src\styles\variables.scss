// Theme Colors
$primary-color: #ffffff; // Background color
$secondary-color: #f2f2f2; // Secondary background color
$primary-btn-color: #00d1ff; // Primary button color
$secondary-btn-color: #d9d9d9; // Secondary button color
$hover-bg-color: #f7f9fb; // Hover background color
$text-color: #000000; // General text color
$border-color: #dddddd; // Border color for elements
$color-error: #ef4444; // Error color
$color-success: #22c55e; // Success color

// Colors
$color-gray-100: #f3f4f6;
$color-gray-200: #e5e7eb;
$color-gray-300: #d1d5db;
$color-gray-500: #6b7280;
$color-gray-600: #4b5563;
$color-gray-700: #374151;
$color-blue-50: #eff6ff;
$color-blue-700: #1d4ed8;
$color-blue-100: #dbeafe;
$color-red-600: #dc2626;

// Component Colors
$color-background: $primary-color;
$color-background-hover: $hover-bg-color;
$color-text: $text-color;
$color-border: $border-color;
$color-overlay: rgba(0, 0, 0, 0.5);

// Additional colors for organization components
$text-color-primary: $text-color;
$text-color-secondary: $color-gray-600;
$background-color-white: $primary-color;
$background-color-light: $color-gray-100;
$error-color: $color-error;

// Spacing
$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 2rem;

// Border Radius
$border-radius: 0.5rem;
$border-radius-sm: 0.25rem;
$border-radius-md: 0.375rem;

// Font Sizes
$font-size-xs: 0.75rem;
$font-size-sm: 0.875rem;
$font-size-base: 1rem;
$font-size-lg: 1.25rem;
$font-size-xl: 1.5rem;

// Font Weights
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Transitions
$transition-fast: 150ms;
$transition-normal: 250ms;
$transition-slow: 350ms;

// Shadows
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

// Font Families
$font-family-sans-serif: 'Poppins', system-ui, sans-serif;
$font-family-base: $font-family-sans-serif; 