@use '../../../styles/variables' as vars;

.document-type-modal {
  padding: vars.$spacing-lg;

  &__form {
    display: flex;
    flex-direction: column;
    gap: vars.$spacing-lg;
  }

  &__field {
    display: flex;
    flex-direction: column;
    gap: vars.$spacing-xs;
  }

  &__label {
    font-size: vars.$font-size-sm;
    font-weight: vars.$font-weight-medium;
    color: vars.$text-color;
  }

  &__input {
    padding: vars.$spacing-sm vars.$spacing-md;
    border: 1px solid vars.$border-color;
    border-radius: vars.$border-radius-sm;
    font-size: vars.$font-size-base;
    font-family: vars.$font-family-sans-serif;
    transition: border-color 0.2s ease;

    &:focus {
      outline: none;
      border-color: vars.$primary-btn-color;
      box-shadow: 0 0 0 2px rgba(0, 209, 255, 0.1);
    }

    &:disabled {
      background-color: vars.$color-gray-100;
      cursor: not-allowed;
    }

    &::placeholder {
      color: vars.$color-gray-500;
    }
  }

  &__error {
    color: vars.$color-error;
    font-size: vars.$font-size-sm;
    margin-top: vars.$spacing-xs;
  }

  &__actions {
    display: flex;
    gap: vars.$spacing-sm;
    justify-content: flex-end;
    margin-top: vars.$spacing-md;
  }
} 