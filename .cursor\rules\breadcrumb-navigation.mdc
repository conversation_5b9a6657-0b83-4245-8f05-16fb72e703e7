---
description: 
globs: 
alwaysApply: true
---
# Feature: Breadcrumb Navigation

A dynamic breadcrumb component must reflect current folder depth:

- Should update as user navigates through Project > Year > Month > Subfolder
- Implement a reusable `Breadcrumb` component in `components/ui/Breadcrumb/`
- Do not hardcode paths — breadcrumb must derive from current route/folder structure
- Breadcrumb should allow clicking to navigate back to higher levels
- Place this component inside the dashboard layout (`components/layout/`)