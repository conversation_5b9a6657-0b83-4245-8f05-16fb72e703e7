import Button from '@/components/ui/Button/Button';
import LoginButton from '@/components/ui/LoginButton/LoginButton';
import OrganizationSelector from '@/components/ui/OrganizationSelector/OrganizationSelector';
import UserMenu from '@/components/ui/UserMenu/UserMenu';
import { Organization, ProjectItem } from '@/types';
import { ChevronDown, ChevronRight } from 'lucide-react';
import React from 'react';
import './Sidebar.scss';

interface SidebarProps {
  projects: ProjectItem[];
  currentPath: string[];
  onProjectClick: (project: ProjectItem) => void;
  onYearClick: (projectName: string, year: string) => void;
  onAddProject: () => void;
  onAddYear: (projectName: string) => void;
  isAuthenticated?: boolean;
  user?: {
    name: string;
    avatarUrl?: string;
  } | null;
  onSignOut?: () => void;
  onProfileSettings?: () => void;
  currentOrganization?: Organization | null;
  availableOrganizations?: Organization[];
  onOrganizationChange?: (organization: Organization) => void;
  organizationLoading?: boolean;
  organizationError?: string | null;
}

const Sidebar: React.FC<SidebarProps> = ({
  projects,
  currentPath,
  onProjectClick,
  onYearClick,
  onAddProject,
  onAddYear,
  isAuthenticated = false,
  user = null,
  onSignOut,
  onProfileSettings,
  currentOrganization,
  availableOrganizations = [],
  onOrganizationChange,
  organizationLoading = false,
  organizationError = null,
}) => {
  return (
    <div className="dashboard__sidebar">
      <div className="dashboard__sidebar-header">
        <h1 className="dashboard__sidebar-title">My Projects</h1>
      </div>
      <div className="dashboard__sidebar-projects">
        {projects.map(project => (
          <div key={project.fullPath} className="dashboard__project-item">
            <div
              className={`dashboard__project-name ${project.isExpanded ? 'expanded' : ''} ${
                currentPath[0] === project.name ? 'active' : ''
              }`}
              onClick={() => onProjectClick(project)}
            >
              {project.isExpanded ? (
                <ChevronDown size={16} className="dashboard__project-arrow" />
              ) : (
                <ChevronRight size={16} className="dashboard__project-arrow" />
              )}
              {project.name}
            </div>
            {project.isExpanded && (
              <div className="dashboard__project-content">
                {project.years && (
                  <div className="dashboard__years-list">
                    {project.years.map(year => (
                      <div
                        key={year.fullPath}
                        className={`dashboard__year-item ${
                          currentPath[0] === project.name && currentPath[1] === year.name ? 'active' : ''
                        }`}
                        onClick={() => onYearClick(project.name, year.name)}
                      >
                        {year.name}
                      </div>
                    ))}
                  </div>
                )}
                <div className="dashboard__add-year">
                  <div
                    className="dashboard__add-year-item"
                    onClick={e => {
                      e.stopPropagation();
                      onAddYear(project.name);
                    }}
                  >
                    + Add year
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
        {projects.length === 0 && (
          <div className="dashboard__empty-state">
            <div className="dashboard__empty-title">No projects yet</div>
            <div className="dashboard__empty-desc">Click "Create new" to create your first project</div>
          </div>
        )}
        <div className="dashboard__create-project">
          <Button variant="gray" size="small" onClick={onAddProject}>
            Create new
          </Button>
        </div>
      </div>
      <div className="dashboard__sidebar-footer">
        <div className="dashboard__user-section">
          {isAuthenticated && user && onSignOut ? (
            <div className="dashboard__user-content">
              <div className="dashboard__user-greeting">Hi, {user.name}</div>
              {currentOrganization && onOrganizationChange && (
                <div className="dashboard__organization-section">
                  <OrganizationSelector
                    currentOrganization={currentOrganization}
                    availableOrganizations={availableOrganizations}
                    onOrganizationChange={onOrganizationChange}
                    isLoading={organizationLoading}
                    error={organizationError}
                  />
                </div>
              )}
              <UserMenu
                userName={user.name}
                avatarUrl={user.avatarUrl}
                onSignOut={onSignOut}
                onProfileSettings={onProfileSettings}
              />
              <img src="/explain-logo.svg" alt="Explain Logo" className="dashboard__sidebar-logo" />
            </div>
          ) : (
            <LoginButton />
          )}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
