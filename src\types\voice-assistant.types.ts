// Web Speech API Types
export interface SpeechRecognitionEvent extends Event {
  resultIndex: number;
  results: {
    [index: number]: {
      [index: number]: {
        transcript: string;
      };
    };
  };
}

export interface SpeechRecognitionErrorEvent extends Event {
  error: string;
}

export interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onresult: (event: SpeechRecognitionEvent) => void;
  onerror: (event: SpeechRecognitionErrorEvent) => void;
  onend: () => void;
  start: () => void;
  stop: () => void;
}

// Component Types
export interface ChatMessage {
  id: string;
  text: string | React.ReactNode;
  type: 'user' | 'assistant';
  timestamp: Date;
}

export interface VoiceInterfaceProps {
  variant?: 'default' | 'panel';
  currentProject?: string;
  onExpandedChange?: (isExpanded: boolean) => void;
}

// Global Type Augmentation
declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition;
    webkitSpeechRecognition: new () => SpeechRecognition;
  }
}
