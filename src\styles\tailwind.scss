@use './fonts.scss';
@use 'tailwindcss/base';
@use 'tailwindcss/components';
@use 'tailwindcss/utilities';

:root {
  --background: 0 0% 100%;
  --foreground: 0 0% 0%;

  --card: 0 0% 100%;
  --card-foreground: 0 0% 0%;

  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 0%;

  --primary: 192 100% 50%;  /* #00D1FF */
  --primary-foreground: 0 0% 100%;

  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;

  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;

  --accent: 192 100% 50%;  /* #00D1FF */
  --accent-foreground: 0 0% 0%;

  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;

  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 192 100% 50%;  /* #00D1FF */

  --radius: 0.5rem;

  --sidebar-background: 0 0% 98%;
  --sidebar-foreground: 0 0% 0%;
  --sidebar-primary: 192 100% 50%;  /* #00D1FF */
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 192 100% 95%;
  --sidebar-accent-foreground: 0 0% 0%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 192 100% 50%;  /* #00D1FF */
}

.dark {
  --background: 0 0% 0%;
  --foreground: 0 0% 100%;

  --card: 0 0% 0%;
  --card-foreground: 0 0% 100%;

  --popover: 0 0% 0%;
  --popover-foreground: 0 0% 100%;

  --primary: 192 100% 50%;  /* #00D1FF */
  --primary-foreground: 0 0% 0%;

  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;

  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;

  --accent: 192 100% 50%;  /* #00D1FF */
  --accent-foreground: 0 0% 0%;

  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;

  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 192 100% 50%;  /* #00D1FF */
  --sidebar-background: 0 0% 10%;
  --sidebar-foreground: 0 0% 100%;
  --sidebar-primary: 192 100% 50%;  /* #00D1FF */
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 0 0% 15.9%;
  --sidebar-accent-foreground: 0 0% 100%;
  --sidebar-border: 0 0% 15.9%;
  --sidebar-ring: 192 100% 50%;  /* #00D1FF */
}

* {
  border-color: var(--border);
}

body {
  background-color: var(--background);
  color: var(--foreground);
}
