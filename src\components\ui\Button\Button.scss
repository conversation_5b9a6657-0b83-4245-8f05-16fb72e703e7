@use "@/styles/variables" as *;
@use "../../../styles/variables" as vars;

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: vars.$spacing-sm;
  padding: vars.$spacing-sm vars.$spacing-md;
  border-radius: vars.$border-radius-sm;
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-normal;
  font-family: vars.$font-family-sans-serif;
  cursor: pointer;
  transition: all vars.$transition-fast;
  text-decoration: none;
  white-space: nowrap;
  min-height: 36px;
  border: none;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &--primary {
    background-color: vars.$primary-btn-color;
    color: vars.$text-color;

    &:hover:not(:disabled) {
      background-color: #00b8e6;
    }
  }

  &--secondary {
    background-color: vars.$secondary-btn-color;
    color: vars.$text-color;

    &:hover:not(:disabled) {
      background-color: #c4c4c4;
    }
  }

  &--gray {
    background-color: vars.$color-gray-200;
    color: vars.$text-color;
    border: 1px solid vars.$color-gray-300;

    &:hover:not(:disabled) {
      background-color: vars.$color-gray-300;
    }
  }

  &--ghost {
    background-color: transparent;
    color: vars.$text-color;
    border: 1px solid vars.$border-color;

    &:hover:not(:disabled) {
      background-color: vars.$color-background-hover;
    }
  }

  &--small {
    padding: vars.$spacing-xs vars.$spacing-sm;
    font-size: vars.$font-size-xs;
  }

  &--medium {
    padding: vars.$spacing-sm vars.$spacing-md;
    font-size: vars.$font-size-sm;
  }

  &--large {
    padding: vars.$spacing-md vars.$spacing-lg;
    font-size: vars.$font-size-base;
  }

  &--icon-only {
    padding: vars.$spacing-sm;
    min-width: 36px;
    min-height: 36px;
  }

  &--loading {
    cursor: wait;
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: vars.$font-size-lg;
    line-height: 1;
  }

  &__spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: button-spin 1s linear infinite;
  }

  &__spinner-svg {
    width: vars.$font-size-lg;
    height: vars.$font-size-lg;
  }

  &__text {
    font-family: vars.$font-family-sans-serif;
  }
}

@keyframes button-spin {
  100% {
    transform: rotate(360deg);
  }
} 