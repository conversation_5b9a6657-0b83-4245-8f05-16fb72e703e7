import React, { useState } from 'react';
import Modal from '@/components/ui/Modal/Modal';
import Button from '@/components/ui/Button/Button';
import './DocumentTypeModal.scss';

interface DocumentTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateDocumentType: (name: string) => Promise<void>;
  existingFolders: Array<{ name: string }>;
}

const DocumentTypeModal: React.FC<DocumentTypeModalProps> = ({
  isOpen,
  onClose,
  onCreateDocumentType,
  existingFolders,
}) => {
  const [documentTypeName, setDocumentTypeName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!documentTypeName.trim()) {
      setError('Document type name is required');
      return;
    }

    const trimmedName = documentTypeName.trim();
    
    // Check if folder already exists
    const exists = existingFolders.some(folder => 
      folder.name.toLowerCase() === trimmedName.toLowerCase()
    );
    
    if (exists) {
      setError('A document type with this name already exists');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await onCreateDocumentType(trimmedName);
      setDocumentTypeName('');
      onClose();
    } catch (error) {
      setError('Failed to create document type. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setDocumentTypeName('');
    setError('');
    onClose();
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose} 
      title="New Document Type"
      description="Create a new document type folder to organize your files."
    >
      <div className="document-type-modal">
        <form onSubmit={handleSubmit} className="document-type-modal__form">
          <div className="document-type-modal__field">
            <label htmlFor="documentTypeName" className="document-type-modal__label">
              Document Type Name
            </label>
            <input
              id="documentTypeName"
              type="text"
              value={documentTypeName}
              onChange={(e) => setDocumentTypeName(e.target.value)}
              placeholder="e.g., Invoices, Receipts, Contracts"
              className="document-type-modal__input"
              disabled={isLoading}
              autoFocus
            />
            {error && <div className="document-type-modal__error">{error}</div>}
          </div>
          
          <div className="document-type-modal__actions">
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isLoading}
              disabled={!documentTypeName.trim()}
            >
              Create Document Type
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default DocumentTypeModal; 