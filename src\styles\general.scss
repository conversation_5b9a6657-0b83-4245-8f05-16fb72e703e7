@use "sass:color";
@use "./variables" as *;

// TODO: This file is an idea of how some general styles could look like, it hasn't really been reviewed, generated by ChatGPT with the colors I gave it

#root {
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
}

// Body Styles
body {
  background-color: $primary-color;
  color: $text-color;
  font-family: 'Poppins', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  margin: 0 auto;
  padding: 0;
  place-items: center;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
}

// Headings
h1,
h2,
h3,
h4,
h5,
h6 {
  color: $text-color;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
}
h2 {
  font-size: 2rem;
}
h3 {
  font-size: 1.75rem;
}
h4 {
  font-size: 1.5rem;
}
h5 {
  font-size: 1.25rem;
}
h6 {
  font-size: 1rem;
}

// Paragraphs and Text
p {
  margin-bottom: 1rem;
}

// Buttons
button,
.btn {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  border-radius: 4px;
  border: none;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;

  &.primary {
    background-color: $primary-btn-color;
    color: $primary-color;

    &:hover {
      background-color: color.scale($primary-btn-color, $lightness: -10%);
    }
  }

  &.secondary {
    background-color: $secondary-btn-color;
    color: $text-color;

    &:hover {
      background-color: color.scale($secondary-btn-color, $lightness: -5%);
    }
  }
}

// Links
a {
  color: $primary-btn-color;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

// Input and Form Elements
input,
textarea,
select {
  width: 100%;
  padding: 0.5rem;
  font-size: 1rem;
  background-color: $secondary-color;
  border: 1px solid color.scale($primary-btn-color, $lightness: 10%);
  border-radius: 4px;
  margin-bottom: 1rem;

  &:focus {
    border-color: color.scale($primary-btn-color, $lightness: 10%);
    outline: none;
  }
}

// Containers
.container {
  padding: 1rem;
  background-color: $primary-color;
}

// Hover Effect Example
.hover-box {
  background-color: $secondary-color;
  padding: 1rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: $hover-bg-color;
  }
}

// Dividers
hr {
  border: 0;
  height: 1px;
  background-color: $border-color;
  margin: 2rem 0;
}
