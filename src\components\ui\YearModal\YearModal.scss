@use "../../../styles/variables" as vars;

.year-modal {
  &__title {
    font-size: vars.$font-size-lg;
    font-weight: vars.$font-weight-medium;
    color: vars.$text-color;
    margin-bottom: vars.$spacing-md;
  }

  &__form {
    display: flex;
    flex-direction: column;
    gap: vars.$spacing-md;
  }

  &__input-group {
    display: flex;
    flex-direction: column;
    gap: vars.$spacing-xs;
  }

  &__label {
    font-size: vars.$font-size-sm;
    font-weight: vars.$font-weight-medium;
    color: vars.$color-text;
  }

  &__input {
    padding: vars.$spacing-sm vars.$spacing-md;
    border: 1px solid vars.$border-color;
    border-radius: vars.$border-radius-sm;
    font-size: vars.$font-size-base;
    transition: border-color vars.$transition-fast;
    background-color: vars.$color-background;

    &:focus {
      outline: none;
      border-color: vars.$primary-btn-color;
    }

    &:disabled {
      background-color: vars.$secondary-color;
      cursor: not-allowed;
    }
  }

  &__error {
    color: vars.$color-error;
    font-size: vars.$font-size-sm;
    margin-top: vars.$spacing-xs;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: vars.$spacing-md;
  }

  &__button {
    background-color: #00b8e6;
    color: vars.$text-color;
    border: none;
    padding: vars.$spacing-sm vars.$spacing-md;
    border-radius: vars.$border-radius-sm;
    font-size: vars.$font-size-base;
    cursor: pointer;
    transition: background-color vars.$transition-fast;

    &:hover {
      background-color: #00b8e6;
    }
  }
} 