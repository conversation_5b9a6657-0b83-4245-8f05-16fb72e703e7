@use "../../../styles/variables" as vars;

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: vars.$color-overlay;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: vars.$spacing-md;
}

.modal {
  background-color: vars.$primary-color;
  border-radius: vars.$border-radius;
  box-shadow: vars.$shadow-lg;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;

  &__header {
    padding: vars.$spacing-lg;
    border-bottom: 1px solid vars.$border-color;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: vars.$spacing-md;
  }

  &__header-content {
    flex: 1;
    min-width: 0;
  }

  &__title {
    font-size: vars.$font-size-lg;
    font-weight: vars.$font-weight-semibold;
    color: vars.$text-color;
    margin: 0 0 vars.$spacing-xs 0;
  }

  &__description {
    font-size: vars.$font-size-sm;
    color: vars.$color-gray-600;
    margin: 0;
    line-height: 1.4;
  }

  &__close {
    background: none;
    border: none;
    cursor: pointer;
    padding: vars.$spacing-xs;
    border-radius: vars.$border-radius-sm;
    color: vars.$color-gray-500;
    transition: all vars.$transition-fast;

    &:hover {
      background-color: vars.$color-background-hover;
      color: vars.$text-color;
    }
  }

  &__content {
    padding: vars.$spacing-lg;
  }

  &__footer {
    padding: vars.$spacing-lg;
    border-top: 1px solid vars.$border-color;
    display: flex;
    gap: vars.$spacing-sm;
    justify-content: flex-end;
  }
} 