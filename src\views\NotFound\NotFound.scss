@use "sass:color";
@use "../../styles/variables" as vars;

.not-found {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  font-family: 'Poppins', system-ui, sans-serif;
  padding: vars.$spacing-lg;
  
  &__content {
    text-align: center;
  }
  
  &__title {
    font-size: vars.$font-size-xl;
    font-weight: vars.$font-weight-bold;
    color: vars.$text-color;
    margin-bottom: vars.$spacing-md;
  }
  
  &__message {
    font-size: vars.$font-size-base;
    color: vars.$color-gray-500;
    margin-bottom: vars.$spacing-lg;
  }
  
  &__link {
    color: color.scale(#00D1FF, $lightness: -10%);
    text-decoration: underline;
    font-family: 'Poppins', system-ui, sans-serif;
    
    &:hover {
      color: color.scale(#00D1FF, $lightness: -20%);
    }
  }

  &__button {
    background-color: #00b8e6;
    color: vars.$text-color;
    border: none;
    padding: vars.$spacing-sm vars.$spacing-md;
    border-radius: vars.$border-radius-sm;
    font-size: vars.$font-size-base;
    cursor: pointer;
    transition: background-color vars.$transition-fast;

    &:hover {
      background-color: #00b8e6;
    }
  }
}
