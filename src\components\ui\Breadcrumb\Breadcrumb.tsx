import React from 'react';
import './Breadcrumb.scss';

interface BreadcrumbProps {
  path: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ path }) => {
  const parts = path.split('/').filter(Boolean);
  const breadcrumbs = parts.map((part, index) => {
    const isLast = index === parts.length - 1;
    
    return (
      <div key={`${part}-${index}`} className="breadcrumb__item">
        {index > 0 && <span className="breadcrumb__separator">/</span>}
        <span className="breadcrumb__text">
          {part}
        </span>
      </div>
    );
  });

  return (
    <div className="breadcrumb">
      <nav className="breadcrumb__nav">{breadcrumbs}</nav>
    </div>
  );
};

export default Breadcrumb;
