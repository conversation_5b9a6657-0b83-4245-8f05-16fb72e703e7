import OrganizationSelector from '@/components/ui/OrganizationSelector/OrganizationSelector';
import { useOrganization } from '@/hooks/useOrganization';
import React from 'react';
import './OrganizationDemo.scss';

const OrganizationDemo: React.FC = () => {
  const { currentOrganization, availableOrganizations, setCurrentOrganization, isLoading, error } = useOrganization();

  return (
    <div className="organization-demo">
      <h2 className="organization-demo__title">Organization Management Demo</h2>
      <div className="organization-demo__section">
        <h3 className="organization-demo__section-title">Current Organization</h3>
        {currentOrganization ? (
          <div className="organization-demo__current">
            <p>
              <strong>ID:</strong> {currentOrganization.id}
            </p>
            <p>
              <strong>Name:</strong> {currentOrganization.name}
            </p>
            <p>
              <strong>Display Name:</strong> {currentOrganization.displayName}
            </p>
          </div>
        ) : (
          <p className="organization-demo__no-org">No organization selected</p>
        )}
      </div>

      <div className="organization-demo__section">
        <h3 className="organization-demo__section-title">Organization Selector</h3>
        <OrganizationSelector
          currentOrganization={currentOrganization}
          availableOrganizations={availableOrganizations}
          onOrganizationChange={setCurrentOrganization}
          isLoading={isLoading}
          error={error}
        />
      </div>

      <div className="organization-demo__section">
        <h3 className="organization-demo__section-title">Available Organizations</h3>
        {availableOrganizations.length > 0 ? (
          <ul className="organization-demo__list">
            {availableOrganizations.map(org => (
              <li key={org.id} className="organization-demo__list-item">
                <strong>{org.displayName}</strong> (ID: {org.id})
              </li>
            ))}
          </ul>
        ) : (
          <p className="organization-demo__no-orgs">No organizations available</p>
        )}
      </div>

      {error && (
        <div className="organization-demo__section">
          <h3 className="organization-demo__section-title">Error</h3>
          <p className="organization-demo__error">{error}</p>
        </div>
      )}
    </div>
  );
};

export default OrganizationDemo;
