import React from 'react';
import FileUploader from '../FileUploader/FileUploader';
import Modal from '../Modal/Modal';
import './FileUploadModal.scss';

interface FileUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (files: File[]) => Promise<void>;
  allowedFileTypes?: string[];
  maxFiles?: number;
}

const FileUploadModal: React.FC<FileUploadModalProps> = ({ isOpen, onClose, onUpload, allowedFileTypes, maxFiles }) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Upload Files">
      <FileUploader onUpload={onUpload} allowedFileTypes={allowedFileTypes} maxFiles={maxFiles} />
    </Modal>
  );
};

export default FileUploadModal;
