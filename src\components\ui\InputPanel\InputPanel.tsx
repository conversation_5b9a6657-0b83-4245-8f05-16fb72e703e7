import Button from '@/components/ui/Button/Button';
import FileItem from '@/components/ui/FileItem/FileItem';
import FolderTabs from '@/components/ui/FolderTabs/FolderTabs';
import ViewToggle, { ViewMode } from '@/components/ui/ViewToggle/ViewToggle';
import { FileItem as FileItemType, NavigableFolderItem } from '@/types';
import React, { useState } from 'react';
import './InputPanel.scss';

interface InputPanelProps {
  folders: NavigableFolderItem[];
  activeTab: string;
  files: FileItemType[];
  onTabClick: (folder: NavigableFolderItem) => void;
  onAddNew: () => void;
  onFileAction: (action: 'download' | 'delete', file: FileItemType) => void;
  onUpload: () => void;
  isLoading: boolean;
  processedFiles: string[];
  checkingFiles: string[];
}

const InputPanel: React.FC<InputPanelProps> = ({
  folders,
  activeTab,
  files,
  onTabClick,
  onAddNew,
  onFileAction,
  onUpload,
  isLoading,
  processedFiles,
  checkingFiles,
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>('list');

  return (
    <div className="input-panel">
      <FolderTabs
        folders={folders}
        activeTab={activeTab}
        onTabClick={onTabClick}
        onAddNew={onAddNew}
        showAddButton={true}
        title="Documents"
      />

      <div className="input-panel__content">
        <div className="input-panel__actions">
          <Button variant="gray" size="small" onClick={onUpload}>
            Upload
          </Button>
          <ViewToggle currentView={viewMode} onViewChange={setViewMode} />
        </div>

        {isLoading ? (
          <div className="input-panel__loading">
            <div className="input-panel__loading-spinner"></div>
            <div className="input-panel__loading-text">Loading...</div>
          </div>
        ) : activeTab ? (
          files.length === 0 ? (
            <div className="input-panel__empty-state">
              <div className="input-panel__empty-desc">No files found in this folder. Upload files to get started.</div>
            </div>
          ) : (
            <div className={`input-panel__files input-panel__files--${viewMode}`}>
              {files.map(item => (
                <FileItem
                  key={item.path}
                  file={{
                    name: item.name,
                    type: 'file',
                    path: item.path,
                  }}
                  onAction={action => onFileAction(action, item)}
                  isProcessed={processedFiles.includes(item.name)}
                  isChecking={checkingFiles.includes(item.name)}
                  viewMode={viewMode}
                />
              ))}
            </div>
          )
        ) : (
          <div className="input-panel__empty-state">
            <div className="input-panel__empty-title">Select a tab</div>
            <div className="input-panel__empty-desc">
              Select a tab above to view files or create a new document type.
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InputPanel;
