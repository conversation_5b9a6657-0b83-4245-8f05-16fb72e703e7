import { CognitoIdentityClient } from '@aws-sdk/client-cognito-identity';
import { DynamoDBClient, PutItemCommand } from '@aws-sdk/client-dynamodb';
import { fromCognitoIdentityPool } from '@aws-sdk/credential-provider-cognito-identity';
import { marshall } from '@aws-sdk/util-dynamodb';
import { NextResponse } from 'next/server';

const cognitoIdentityClient = new CognitoIdentityClient({
  region: process.env.AWS_REGION,
});

const getDynamoClient = async (idToken: string) => {
  const credentialsProvider = fromCognitoIdentityPool({
    client: cognitoIdentityClient,
    identityPoolId: process.env.COGNITO_IDENTITY_POOL_ID!,
    logins: {
      [`cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.COGNITO_USER_POOL_ID}`]: idToken,
    },
  });

  return new DynamoDBClient({
    region: process.env.AWS_REGION,
    credentials: credentialsProvider,
  });
};

export async function POST(request: Request) {
  try {
    const { filePath, mapping, idToken } = await request.json();

    if (!idToken) {
      return NextResponse.json({ error: 'No ID token provided' }, { status: 401 });
    }

    const dynamoClient = await getDynamoClient(idToken);
    const command = new PutItemCommand({
      TableName: process.env.DYNAMODB_MAPPINGS_TABLE,
      Item: marshall({
        filePath,
        mapping,
        createdAt: new Date().toISOString(),
      }),
    });

    await dynamoClient.send(command);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error saving mapping:', error);
    return NextResponse.json({ error: 'Failed to save mapping' }, { status: 500 });
  }
}
