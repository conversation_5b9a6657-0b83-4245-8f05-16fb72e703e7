---
description: 
globs: 
alwaysApply: true
---
# Feature: Auth Flow (Supabase)

The application uses Supabase for frontend-only authentication.

- Login form is located at `src/views/Login/Login.tsx`
- Auth logic must be abstracted into `services/authentication.service.ts`
- Optional: wrap logic in a custom hook `hooks/useAuth.ts` for better state management
- Login should currently simulate success — real Supabase keys are not yet integrated
- Do not include validation logic or email confirmation
- Style the login form with a reusable `LoginCard` component using SCSS + Tailwind