@use 'sass:color';
@use '../../../styles/variables' as vars;

.user-menu {
  position: relative;
  font-family: vars.$font-family-sans-serif;
  margin-top: vars.$spacing-xl;
  margin-bottom: vars.$spacing-xl;

  &__trigger {
    display: flex;
    justify-content: center;
    &:hover {
      opacity: 0.8;
    }
  }

  &__avatar {
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid vars.$border-color;
  }

  &__avatar-img {
    display: block;
    width: 48px;
    height: 48px;
  }

  &__button {
    background: none;
    border: none;
    cursor: pointer;
    padding: vars.$spacing-sm;
    border-radius: vars.$border-radius-sm;
    display: flex;
    align-items: center;
    gap: vars.$spacing-sm;
    color: vars.$text-color;
    font-size: vars.$font-size-base;
    font-weight: vars.$font-weight-normal;
    transition: background-color vars.$transition-fast;

    &:hover {
      background-color: vars.$color-background-hover;
    }
  }

  &__dropdown {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: vars.$primary-color;
    border: 1px solid vars.$border-color;
    border-radius: vars.$border-radius-sm;
    box-shadow: vars.$shadow-md;
    min-width: 120px;
    z-index: 1000;
    margin-bottom: vars.$spacing-xs;
    padding: vars.$spacing-xs 0;

    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-top: 6px solid vars.$primary-color;
    }

    &::before {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 7px solid transparent;
      border-right: 7px solid transparent;
      border-top: 7px solid vars.$border-color;
    }
  }

  &__item {
    width: 100%;
    padding: vars.$spacing-sm vars.$spacing-md;
    text-align: left;
    color: vars.$text-color;
    background: none;
    border: none;
    cursor: pointer;
    transition: background-color vars.$transition-fast;
    font-family: vars.$font-family-sans-serif;
    font-size: vars.$font-size-sm;

    &:hover {
      background-color: vars.$color-background-hover;
    }

    &:first-child {
      border-radius: vars.$border-radius-sm vars.$border-radius-sm 0 0;
    }

    &:last-child {
      border-radius: 0 0 vars.$border-radius-sm vars.$border-radius-sm;
    }

    &:not(:last-child) {
      border-bottom: 1px solid vars.$border-color;
    }
  }
}
