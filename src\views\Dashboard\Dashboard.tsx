import Sidebar from '@/components/layout/Sidebar/Sidebar';
import Breadcrumb from '@/components/ui/Breadcrumb/Breadcrumb';
import Button from '@/components/ui/Button/Button';
import DocumentTypeModal from '@/components/ui/DocumentTypeModal/DocumentTypeModal';
import FileItem from '@/components/ui/FileItem/FileItem';
import FileUploadModal from '@/components/ui/FileUploadModal/FileUploadModal';
import FolderItem from '@/components/ui/FolderItem/FolderItem';
import InputPanel from '@/components/ui/InputPanel/InputPanel';
import OutputPanel from '@/components/ui/OutputPanel/OutputPanel';
import ProjectModal from '@/components/ui/ProjectModal/ProjectModal';
import VoiceInterface from '@/components/ui/VoiceInterface/VoiceInterface';
import YearModal from '@/components/ui/YearModal/YearModal';
import { useAuth } from '@/contexts/AuthContext';
import { useAudit } from '@/hooks/useAudit';
import { useOrganization } from '@/hooks/useOrganization';
import { checkFilesOpenSearch, deleteFilesOpenSearch } from '@/services/audit.service';
import { createFolder, deleteFile, listFiles, listFolders, uploadFile } from '@/services/file-storage.service';
import { createIndex } from '@/services/search.service';
import { FileItem as FileItemType, NavigableFolderItem } from '@/types';
import { FolderPlus, ShieldCheck } from 'lucide-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ProjectItem } from '../../types';
import './Dashboard.scss';

const STANDARD_FOLDERS = ['General Ledger'];
const ROOT_TITLE = 'My Projects';

// File type restrictions
const FILE_TYPE_RESTRICTIONS = {
  'General Ledger': ['.xlsx', '.xls', '.csv'],
};

const Dashboard: React.FC = () => {
  const [currentPath, setCurrentPath] = useState<string[]>([]);
  const [sidebarProjects, setSidebarProjects] = useState<ProjectItem[]>([]);
  const [dashboardContent, setDashboardContent] = useState<ProjectItem[]>([]);
  const [processedFiles, setProcessedFiles] = useState<string[]>([]);
  const [checkingFiles, setCheckingFiles] = useState<string[]>([]);
  const [isProjectModalOpen, setIsProjectModalOpen] = useState(false);
  const [isYearModalOpen, setIsYearModalOpen] = useState(false);
  const [isFileUploadModalOpen, setIsFileUploadModalOpen] = useState(false);
  const [isDocumentTypeModalOpen, setIsDocumentTypeModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isVoicePanelExpanded, setIsVoicePanelExpanded] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('');
  const { isAuditing, auditStatus, auditProgress, handleGeneralLedgerAudit } = useAudit();
  const { isAuthenticated, isLoading: isAuthLoading, user, logout } = useAuth();
  const {
    currentOrganization,
    availableOrganizations,
    setCurrentOrganization,
    isLoading: isOrganizationLoading,
    error: organizationError,
  } = useOrganization();
  const [folderTabs, setFolderTabs] = useState<NavigableFolderItem[]>([]);
  const [tabFiles, setTabFiles] = useState<FileItemType[]>([]);

  // Split panel system variables
  const [bottomPanelTop, setBottomPanelTop] = useState(0);
  const isDraggingRef = useRef(false);
  const startYRef = useRef(0);
  const startTopRef = useRef(0);

  // Dynamic height calculations
  const windowHeight = window.innerHeight;
  const topTitleHeight = 76; // Height of InputPanel title area
  const bottomTitleHeight = 80; // Height of OutputPanel title area

  const folderState = useMemo(() => {
    const isInProject = currentPath.length === 1;
    const isInFiscalYear = currentPath.length === 2;
    const isInStandardFolder = currentPath.length === 3 && STANDARD_FOLDERS.includes(currentPath[2]);
    const isInUploadableFolder = isInStandardFolder;
    const showAuditButton = isInStandardFolder && currentPath[2] === 'General Ledger';

    return {
      isInProject,
      isInFiscalYear,
      isInStandardFolder,
      isInUploadableFolder,
      showAuditButton,
    };
  }, [currentPath]);

  // Initialize bottom panel position
  useEffect(() => {
    if (folderState.isInFiscalYear && bottomPanelTop === 0) {
      setBottomPanelTop(windowHeight * 0.5); // Start at 50% of window height
    }
  }, [folderState.isInFiscalYear, bottomPanelTop, windowHeight]);

  // Update CSS custom properties for dynamic panel positioning
  useEffect(() => {
    if (folderState.isInFiscalYear) {
      const root = document.documentElement;
      root.style.setProperty('--bottom-panel-top', `${bottomPanelTop}px`);
      root.style.setProperty('--top-panel-height', `${bottomPanelTop}px`);
      root.style.setProperty('--drag-handle-top', `${bottomPanelTop - 5}px`);
    }
  }, [bottomPanelTop, folderState.isInFiscalYear]);

  // Drag event handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    isDraggingRef.current = true;
    startYRef.current = e.clientY;
    startTopRef.current = bottomPanelTop;

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDraggingRef.current) return;

    e.preventDefault();
    const deltaY = e.clientY - startYRef.current;
    const newTop = startTopRef.current + deltaY;

    // Calculate constraints
    const minTop = topTitleHeight; // Maximum: top of bottom panel at top title height
    const maxTop = windowHeight - bottomTitleHeight; // Minimum: only bottom title visible

    const constrainedTop = Math.max(minTop, Math.min(maxTop, newTop));
    setBottomPanelTop(constrainedTop);
  };

  const handleMouseUp = () => {
    isDraggingRef.current = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  const loadProjectYears = async (projectName: string) => {
    try {
      const years = await listFolders(projectName);
      return years.map(year => ({
        ...year,
        type: 'folder' as const,
        years: [],
      }));
    } catch (error) {
      console.error('Failed to load years:', error);
      return [];
    }
  };

  // Load projects for sidebar
  useEffect(() => {
    const loadProjects = async () => {
      if (isAuthLoading || !isAuthenticated) return;

      try {
        const folders = await listFolders('');
        const sortedFolders = [...folders].sort((a, b) => a.name.localeCompare(b.name));
        const projectsWithYears = await Promise.all(
          sortedFolders.map(async project => ({
            ...project,
            type: 'folder' as const,
            years: await loadProjectYears(project.name),
            isExpanded: false,
          }))
        );
        setSidebarProjects(projectsWithYears);
      } catch (error) {
        console.error('Failed to load projects:', error);
      }
    };

    loadProjects();
  }, [isAuthenticated, isAuthLoading]);

  // Refresh data when organization changes
  useEffect(() => {
    if (currentOrganization) {
      // Reset current path to root when organization changes
      setCurrentPath([]);
      setActiveTab('');
      setDashboardContent([]);
      setTabFiles([]);
      setFolderTabs([]);
      setProcessedFiles([]);
      setCheckingFiles([]);

      // Reload projects for the new organization
      const loadProjects = async () => {
        if (isAuthLoading || !isAuthenticated) return;

        try {
          const folders = await listFolders('');
          const sortedFolders = [...folders].sort((a, b) => a.name.localeCompare(b.name));
          const projectsWithYears = await Promise.all(
            sortedFolders.map(async project => ({
              ...project,
              type: 'folder' as const,
              years: await loadProjectYears(project.name),
              isExpanded: false,
            }))
          );
          setSidebarProjects(projectsWithYears);
        } catch (error) {
          console.error('Failed to load projects for new organization:', error);
        }
      };

      loadProjects();
    }
  }, [currentOrganization, isAuthenticated, isAuthLoading]);

  // Load content for main dashboard
  useEffect(() => {
    const loadContent = async () => {
      if (isAuthLoading || !isAuthenticated || currentPath.length === 0) return;

      setIsLoading(true);
      try {
        const path = currentPath.join('/');
        const isStandard = STANDARD_FOLDERS.includes(currentPath[currentPath.length - 1]);

        if (isStandard) {
          // List files in standard folders
          const files = await listFiles(path);
          const fileItems = files.map(file => ({
            ...file,
            type: 'file' as const,
          }));
          setDashboardContent(fileItems);

          // Set all files as being checked initially
          setCheckingFiles(fileItems.map(file => file.name));

          // Check which files are processed in OpenSearch asynchronously
          checkFilesOpenSearch(path)
            .then(checkResponse => {
              const processedFileNames = checkResponse.processed_files.map(filePath => {
                const parts = filePath.split('/');
                return parts[parts.length - 1];
              });
              setProcessedFiles(processedFileNames);
              setCheckingFiles([]);
            })
            .catch(error => {
              console.error('Failed to check files in OpenSearch:', error);
              setCheckingFiles([]);
            });
        } else if (folderState.isInFiscalYear && activeTab) {
          // Load files for the active tab - but don't check OpenSearch here
          // The third useEffect will handle the OpenSearch check
          const files = await listFiles(activeTab);
          const fileItems = files.map(file => ({
            ...file,
            type: 'file' as const,
          }));
          setDashboardContent(fileItems);
          setCheckingFiles(fileItems.map(file => file.name));
        } else {
          // List folders
          const folders = await listFolders(path);
          let sortedFolders = [...folders];

          if (currentPath.length === 1) {
            // At project level, sort fiscal years in descending order
            sortedFolders.sort((a, b) => {
              const yearA = parseInt(a.name.replace('FY', ''));
              const yearB = parseInt(b.name.replace('FY', ''));
              return yearB - yearA;
            });
          }

          const folderItems = sortedFolders.map(folder => ({
            ...folder,
            type: 'folder' as const,
          }));

          setDashboardContent(folderItems);

          // If we're at fiscal year level and no active tab is set, set the first folder as active
          if (folderState.isInFiscalYear && !activeTab && folderItems.length > 0) {
            const firstFolder = folderItems[0];
            setActiveTab(firstFolder.fullPath);
          }

          setProcessedFiles([]);
        }
      } catch (error) {
        console.error('Failed to load content:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadContent();
  }, [currentPath, activeTab, isAuthenticated, isAuthLoading, folderState.isInFiscalYear]);

  // Update useEffect for loading fiscal year folders and files
  useEffect(() => {
    const loadFiscalYearTabsAndFiles = async () => {
      if (folderState.isInFiscalYear) {
        const path = currentPath.join('/');
        const folders = await listFolders(path);
        const folderItems = folders.map(folder => ({
          ...folder,
          type: 'folder' as const,
          path: folder.fullPath,
        }));
        setFolderTabs(folderItems);
        // Set first tab as active if not already
        if (!activeTab && folderItems.length > 0) {
          setActiveTab(folderItems[0].path);
        }
      }
    };
    loadFiscalYearTabsAndFiles();
  }, [currentPath, folderState.isInFiscalYear]);

  // Update useEffect for loading files for the active tab
  useEffect(() => {
    const loadFilesForTab = async () => {
      if (folderState.isInFiscalYear && activeTab) {
        setIsLoading(true);
        try {
          const files = await listFiles(activeTab);
          const fileItems = files.map(file => ({
            ...file,
            type: 'file' as const,
            path: file.fullPath,
          }));
          setTabFiles(fileItems);
          setCheckingFiles(fileItems.map(file => file.name));
          checkFilesOpenSearch(activeTab)
            .then(checkResponse => {
              const processedFileNames = checkResponse.processed_files.map(filePath => {
                const parts = filePath.split('/');
                return parts[parts.length - 1];
              });
              setProcessedFiles(processedFileNames);
              setCheckingFiles([]);
            })
            .catch(error => {
              console.error('Failed to check files in OpenSearch:', error);
              setCheckingFiles([]);
            });
        } catch (error) {
          console.error('Failed to load files for tab:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };
    loadFilesForTab();
  }, [activeTab, folderState.isInFiscalYear]);

  const handleProjectClick = async (project: ProjectItem) => {
    if (!project.years) {
      project.years = await loadProjectYears(project.name);
    }
    project.isExpanded = !project.isExpanded;
    setSidebarProjects([...sidebarProjects]);
  };

  const handleYearClick = (projectName: string, year: string) => {
    setCurrentPath([projectName, year]);
    setActiveTab('');
  };

  const handleFolderClick = (folderName: string) => {
    const newPath = [...currentPath, folderName];
    setCurrentPath(newPath);
    if (folderState.isInFiscalYear) {
      const folderPath = `${currentPath.join('/')}/${folderName}`;
      setActiveTab(folderPath);
    }
  };

  const handleTabClick = (folder: NavigableFolderItem) => {
    // Don't change currentPath, only change the active tab
    // This keeps us at the fiscal year level and only changes which folder's files are shown
    setActiveTab(folder.path);
  };

  const handleAddNewTab = () => {
    setIsDocumentTypeModalOpen(true);
  };

  const handleCreateDocumentType = async (documentTypeName: string) => {
    try {
      const path = currentPath.join('/');
      const newFolderPath = `${path}/${documentTypeName}`;
      await createFolder(newFolderPath);

      // Refresh the folder tabs
      const folders = await listFolders(path);
      const folderItems = folders.map(folder => ({
        ...folder,
        type: 'folder' as const,
        path: folder.fullPath,
      }));
      setFolderTabs(folderItems);

      // Set the new folder as the active tab and clear tab files
      const newFolder = folderItems.find(folder => folder.name === documentTypeName);
      if (newFolder) {
        setActiveTab(newFolder.path);
        setTabFiles([]); // Clear files since new folder will be empty
      }
    } catch (error) {
      console.error('Failed to create document type:', error);
      throw error;
    }
  };

  const handleCreateProject = async (projectName: string, fiscalYear: string) => {
    try {
      // Create the project folder in S3
      const projectPath = `${projectName}/${fiscalYear}`;
      await createFolder(projectPath);

      // Create only the General Ledger folder within the fiscal year
      const generalLedgerPath = `${projectPath}/General Ledger`;
      await createFolder(generalLedgerPath);

      // Create the project folder in OpenSearch
      await createIndex({ project_name: projectName });

      // Refresh the current view to show the new project
      const folders = await listFolders('');
      const sortedFolders = [...folders].sort((a, b) => a.name.localeCompare(b.name));
      const projectsWithYears = await Promise.all(
        sortedFolders.map(async project => ({
          ...project,
          type: 'folder' as const,
          years: await loadProjectYears(project.name),
          isExpanded: false,
        }))
      );
      setSidebarProjects(projectsWithYears);
    } catch (error) {
      console.error('Failed to create project:', error);
      throw error;
    }
  };

  const handleCreateFiscalYear = async (fiscalYear: string) => {
    try {
      // Create the fiscal year folder inside the current project
      const fiscalYearPath = [...currentPath, fiscalYear].join('/');
      await createFolder(fiscalYearPath);

      // Create only the General Ledger folder within the fiscal year
      const generalLedgerPath = `${fiscalYearPath}/General Ledger`;
      await createFolder(generalLedgerPath);

      // Refresh the current view to show the new fiscal year
      const folders = await listFolders(currentPath.join('/'));
      setDashboardContent(
        folders.map(folder => ({
          ...folder,
          type: 'folder' as const,
        }))
      );

      // Refresh the sidebar projects to show the new fiscal year
      const allFolders = await listFolders('');
      const sortedFolders = [...allFolders].sort((a, b) => a.name.localeCompare(b.name));
      const projectsWithYears = await Promise.all(
        sortedFolders.map(async project => ({
          ...project,
          type: 'folder' as const,
          years: await loadProjectYears(project.name),
          isExpanded: false,
        }))
      );
      setSidebarProjects(projectsWithYears);
    } catch (error) {
      console.error('Failed to create fiscal year:', error);
      throw error;
    }
  };

  const handleAddYear = async (projectName: string) => {
    try {
      // Get the existing years for this specific project
      const existingYears = await listFolders(projectName);
      const existingFolders = existingYears.map(year => ({
        name: year.name,
        fullPath: year.fullPath,
        type: 'folder' as const,
      }));

      // Set the current path to the project and open the year modal with the correct existing folders
      setCurrentPath([projectName]);
      setDashboardContent(existingFolders);
      setIsYearModalOpen(true);
    } catch (error) {
      console.error('Failed to load existing years:', error);
    }
  };

  const handleFileUpload = async (files: File[]) => {
    try {
      // Use the active tab path if we're at fiscal year level, otherwise use current path
      const uploadPath = folderState.isInFiscalYear && activeTab ? activeTab : currentPath.join('/');

      for (const file of files) {
        await uploadFile(uploadPath, file);
      }

      // Refresh the current view after upload
      if (folderState.isInFiscalYear && activeTab) {
        // Refresh tab files
        const files = await listFiles(activeTab);
        const fileItems = files.map(file => ({
          ...file,
          type: 'file' as const,
          path: file.fullPath,
        }));
        setTabFiles(fileItems);
      } else {
        // Refresh dashboard content for other levels
        const updatedFiles = await listFiles(uploadPath);
        setDashboardContent(
          updatedFiles.map(file => ({
            ...file,
            type: 'file' as const,
          }))
        );
      }
    } catch (error) {
      console.error('Failed to upload files:', error);
      throw error;
    }
  };

  const onAuditClick = () => {
    if (!currentPath.length) return;
    const projectPath = currentPath.slice(0, 2).join('/'); // project/fiscalYear
    const currentFolder = currentPath[currentPath.length - 1];

    if (currentFolder === 'General Ledger') {
      handleGeneralLedgerAudit(projectPath);
    }
  };

  const handleFileAction = async (action: 'download' | 'delete', file: FileItemType) => {
    try {
      if (action === 'download') {
        // Create a temporary link element
        const link = document.createElement('a');
        link.href = file.path;
        link.download = file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else if (action === 'delete') {
        // Delete the file's chunks from OpenSearch
        await deleteFilesOpenSearch(file.path);
        // Delete the file from S3
        await deleteFile(file.path);
        // Refresh the current view after deletion
        const updatedFiles = await listFiles(currentPath.join('/'));
        setDashboardContent(
          updatedFiles.map(file => ({
            ...file,
            type: 'file' as const,
          }))
        );
      }
    } catch (error) {
      console.error(`Failed to ${action} file:`, error);
    }
  };

  const handleSignOut = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleProfileSettings = () => {
    // TODO: Implement profile settings modal
    console.log('Profile settings clicked');
  };

  const handleVoicePanelExpandedChange = (isExpanded: boolean) => {
    setIsVoicePanelExpanded(isExpanded);
  };

  if (isAuthLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-pulse-opacity text-2xl font-semibold font-['Poppins']">Loading...</div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Let the ProtectedRoute handle the redirect
  }

  return (
    <div className="dashboard-view">
      <div className="dashboard__content">
        <Sidebar
          projects={sidebarProjects}
          currentPath={currentPath}
          onProjectClick={handleProjectClick}
          onYearClick={handleYearClick}
          onAddProject={() => setIsProjectModalOpen(true)}
          onAddYear={handleAddYear}
          isAuthenticated={isAuthenticated}
          user={user}
          onSignOut={handleSignOut}
          onProfileSettings={handleProfileSettings}
          currentOrganization={currentOrganization}
          availableOrganizations={availableOrganizations}
          onOrganizationChange={setCurrentOrganization}
          organizationLoading={isOrganizationLoading}
          organizationError={organizationError}
        />
        <div className={`dashboard__main ${!isVoicePanelExpanded ? 'dashboard__main--voice-collapsed' : ''}`}>
          <div className="dashboard__header">
            <div className="dashboard__breadcrumb">
              <Breadcrumb
                path={folderState.isInFiscalYear ? currentPath.slice(0, 2).join('/') : currentPath.join('/')}
              />
            </div>
            <div className="dashboard__actions">
              {folderState.isInProject && (
                <Button variant="primary" icon={<FolderPlus size={18} />} onClick={() => setIsYearModalOpen(true)}>
                  Add Fiscal Year
                </Button>
              )}
              {folderState.showAuditButton && (
                <div className="dashboard__audit-container">
                  <Button
                    variant="primary"
                    icon={<ShieldCheck size={18} />}
                    onClick={onAuditClick}
                    disabled={isAuditing}
                    loading={isAuditing}
                  >
                    {isAuditing ? 'Auditing...' : 'Audit'}
                  </Button>
                  {isAuditing && (
                    <div className="dashboard__audit-progress">
                      <div className="audit-progress__message">{auditStatus}</div>
                      {auditProgress.progressPercentage !== undefined && (
                        <div className="audit-progress__bar">
                          <div
                            className="audit-progress__bar-fill"
                            style={{ width: `${auditProgress.progressPercentage}%` }}
                          />
                          <div className="audit-progress__bar-text">{auditProgress.progressPercentage.toFixed(0)}%</div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          <div className="dashboard__content-area">
            {folderState.isInFiscalYear ? (
              // Split panel system for fiscal year level
              <div className="dashboard__split-panels">
                {/* Top Panel (Input) */}
                <div
                  className={`dashboard__top-panel ${!isVoicePanelExpanded ? 'dashboard__top-panel--voice-collapsed' : ''}`}
                >
                  <InputPanel
                    folders={folderTabs}
                    activeTab={activeTab}
                    files={tabFiles}
                    onTabClick={handleTabClick}
                    onAddNew={handleAddNewTab}
                    onFileAction={handleFileAction}
                    onUpload={() => setIsFileUploadModalOpen(true)}
                    isLoading={isLoading}
                    processedFiles={processedFiles}
                    checkingFiles={checkingFiles}
                  />
                </div>

                {/* Drag Handle */}
                <div className="dashboard__drag-handle" onMouseDown={handleMouseDown}>
                  <div className="dashboard__drag-indicator"></div>
                </div>

                {/* Bottom Panel (Output) */}
                <div
                  className={`dashboard__bottom-panel ${!isVoicePanelExpanded ? 'dashboard__bottom-panel--voice-collapsed' : ''}`}
                >
                  <OutputPanel
                    dataSource="Outputs"
                    basePath={currentPath.join('/')}
                    onFileClick={file => handleFileAction('download', file)}
                  />
                </div>
              </div>
            ) : (
              // Grid view for other levels
              <div className="dashboard__grid">
                {isLoading ? (
                  <div className="dashboard__loading">
                    <div className="dashboard__loading-spinner"></div>
                    <div className="dashboard__loading-text">Loading...</div>
                  </div>
                ) : dashboardContent.length === 0 ? (
                  <div className="dashboard__empty-state">
                    <div className="dashboard__empty-title">No items found</div>
                    <div className="dashboard__empty-desc">
                      {currentPath.length === 0
                        ? 'Click "Create new" to create your first project'
                        : currentPath.length === 1
                          ? 'Click "Add Fiscal Year" to create a new fiscal year'
                          : 'No files found in this folder'}
                    </div>
                  </div>
                ) : (
                  dashboardContent.map(item => {
                    if (item.type === 'folder' && 'path' in item) {
                      const folder = item as NavigableFolderItem;
                      return (
                        <FolderItem key={folder.path} folder={folder} onClick={() => handleFolderClick(folder.name)} />
                      );
                    } else if (item.type === 'file' && 'path' in item) {
                      const file = item as FileItemType;
                      return (
                        <FileItem
                          key={file.path}
                          file={file}
                          onAction={action => handleFileAction(action, file)}
                          isProcessed={processedFiles.includes(file.name)}
                          isChecking={checkingFiles.includes(file.name)}
                        />
                      );
                    }
                    return null;
                  })
                )}
              </div>
            )}
          </div>
        </div>
        <VoiceInterface
          variant="panel"
          currentProject={currentPath[0] || ''}
          onExpandedChange={handleVoicePanelExpandedChange}
        />
      </div>
      <ProjectModal
        isOpen={isProjectModalOpen}
        onClose={() => setIsProjectModalOpen(false)}
        onCreateProject={handleCreateProject}
        existingFolders={sidebarProjects}
      />
      <YearModal
        isOpen={isYearModalOpen}
        onClose={() => setIsYearModalOpen(false)}
        onCreateYear={handleCreateFiscalYear}
        existingFolders={dashboardContent}
      />
      <FileUploadModal
        isOpen={isFileUploadModalOpen}
        onClose={() => setIsFileUploadModalOpen(false)}
        onUpload={handleFileUpload}
        allowedFileTypes={
          folderState.isInFiscalYear && activeTab
            ? FILE_TYPE_RESTRICTIONS[activeTab.split('/').pop() as keyof typeof FILE_TYPE_RESTRICTIONS]
            : FILE_TYPE_RESTRICTIONS[currentPath[2] as keyof typeof FILE_TYPE_RESTRICTIONS]
        }
        maxFiles={5}
      />
      <DocumentTypeModal
        isOpen={isDocumentTypeModalOpen}
        onClose={() => setIsDocumentTypeModalOpen(false)}
        onCreateDocumentType={handleCreateDocumentType}
        existingFolders={folderTabs}
      />
    </div>
  );
};

export default Dashboard;
