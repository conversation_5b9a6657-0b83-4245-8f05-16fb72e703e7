@use "sass:color";
@use "../../../styles/variables" as vars;

.voice-interface {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-md;
  background-color: vars.$primary-color;
  height: 100%;
  font-family: vars.$font-family-sans-serif;
  
  &--panel {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 400px;
    background-color: vars.$primary-color;
    padding: vars.$spacing-md;
    border-left: 1px solid vars.$color-gray-100;
    box-shadow: -2px 0 8px rgba(vars.$text-color, 0.1);
    height: 100vh;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
  }
  
  &--collapsed {
    width: 0;
    padding: 0;
    overflow: visible;
  }
  
  &__toggle {
    position: absolute;
    left: -30px;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 60px;
    background-color: vars.$primary-btn-color;
    border: 1px solid vars.$primary-btn-color;
    border-radius: 8px 0 0 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);

    &:hover {
      background-color: vars.$primary-btn-color;
      box-shadow: -2px 0 6px rgba(0, 0, 0, 0.15);
    }

    svg {
      transition: transform 0.3s ease;
      color: vars.$text-color;
    }
  }
  
  &--expanded &__toggle {
    background-color: vars.$color-gray-100;
    border-color: vars.$color-gray-100;

    &:hover {
      background-color: vars.$color-gray-300;
    }
  }
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: vars.$spacing-md;
    border-bottom: 1px solid vars.$border-color;
  }
  
  &__title {
    font-size: vars.$font-size-lg;
    font-weight: vars.$font-weight-normal;
    color: vars.$text-color;
    margin: 0;
    font-family: vars.$font-family-sans-serif;
  }
  
  &__speaking-indicator {
    font-size: vars.$font-size-sm;
    color: vars.$primary-btn-color;
    animation: pulse 1.5s infinite;
    font-family: vars.$font-family-sans-serif;
  }
  
  &__chat {
    flex: 1;
    overflow-y: auto;
    padding: vars.$spacing-md 0;
    display: flex;
    flex-direction: column;
    gap: vars.$spacing-md;
  }
  
  &__message {
    max-width: 85%;
    display: flex;
    flex-direction: column;
    gap: vars.$spacing-xs;
    
    &--user {
      align-self: flex-end;
      
      .voice-interface__message-content {
        background-color: vars.$primary-btn-color;
        color: vars.$text-color;
        border-radius: vars.$border-radius vars.$border-radius 0 vars.$border-radius;
      }
      
      .voice-interface__message-timestamp {
        text-align: right;
      }
    }
    
    &--assistant {
      align-self: flex-start;
      
      .voice-interface__message-content {
        background-color: vars.$color-gray-100;
        color: vars.$text-color;
        border-radius: vars.$border-radius vars.$border-radius vars.$border-radius 0;
      }
    }
  }
  
  &__message-content {
    padding: vars.$spacing-sm vars.$spacing-md;
    font-size: vars.$font-size-base;
    line-height: 1.5;
    font-family: vars.$font-family-sans-serif;
  }
  
  &__message-timestamp {
    font-size: vars.$font-size-sm;
    color: vars.$color-gray-500;
    padding: 0 vars.$spacing-xs;
    font-family: vars.$font-family-sans-serif;
  }
  
  &__controls {
    display: flex;
    justify-content: center;
    padding: vars.$spacing-md 0;
  }
  
  &__input-area {
    display: flex;
    position: relative;
    margin-top: auto;
  }
  
  &__input {
    width: 100%;
    resize: none;
    border: 1px solid vars.$border-color;
    border-radius: vars.$border-radius;
    padding: vars.$spacing-sm vars.$spacing-xl vars.$spacing-sm vars.$spacing-sm;
    background-color: vars.$primary-color;
    color: vars.$text-color;
    outline: none;
    transition: border-color vars.$transition-fast;
    font-family: vars.$font-family-sans-serif;
    
    &:focus {
      border-color: vars.$primary-btn-color;
      box-shadow: 0 0 0 2px rgba(vars.$primary-btn-color, 0.2);
    }
  }
  
  &__send-button {
    position: absolute;
    right: vars.$spacing-sm;
    bottom: vars.$spacing-sm;
  }

  &__search-result {
    display: flex;
    flex-direction: column;
    gap: vars.$spacing-xs;
  }

  &__search-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: vars.$font-size-sm;
    color: vars.$color-gray-600;
    margin-bottom: vars.$spacing-xs;
  }

  &__document-name {
    font-weight: vars.$font-weight-medium;
  }

  &__score {
    background-color: transparent;
    color: vars.$text-color;
    padding: vars.$spacing-xs vars.$spacing-sm;
    border-radius: vars.$border-radius-sm;
    font-size: vars.$font-size-sm;
    border: 1px solid vars.$primary-btn-color;
  }

  &__search-result-content {
    line-height: 1.5;
    white-space: pre-wrap;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
} 