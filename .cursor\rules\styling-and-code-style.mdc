---
description: 
globs: 
alwaysApply: true
---
# Styling and Code Style

- Use SCSS with BEM for component-specific styles.
- Use Tailwind CSS only for layout, spacing, and responsiveness.
- Never hardcode hex values — always use SCSS variables (stored in `variables.scss`).
- Only import style files inside their matching component file.
- Do not mix inline styles and SCSS unless explicitly instructed.
- When importing other SCSS files into another SCSS file, use '@use' instead of '@import'.
- NEVER use CSS variables or '@apply' directibe in any of the SCSS files
