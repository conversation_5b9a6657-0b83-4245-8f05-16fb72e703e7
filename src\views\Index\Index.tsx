import Login from '@/views/Login/Login';
import Dashboard from '@/views/Dashboard/Dashboard';

const Index = () => {
  const { isAuthenticated, isLoading } = { isAuthenticated: false, isLoading: false };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-pulse-opacity text-2xl font-semibold font-['Poppins']">Loading...</div>
        </div>
      </div>
    );
  }

  return isAuthenticated ? <Dashboard /> : <Login />;
};

export default Index;
