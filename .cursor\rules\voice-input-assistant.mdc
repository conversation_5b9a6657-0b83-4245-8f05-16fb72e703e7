---
description: 
globs: 
alwaysApply: true
---
# Feature: Voice Input Assistant (Web Speech API)

The app features a voice-to-text component using the Web Speech API:

- Create a `VoiceAssistant` component under `components/ui/VoiceAssistant/`
- Must use native browser `SpeechRecognition` to transcribe voice to text
- User presses a button to start recording
- Captured text is displayed in a chat-like interface
- Future integration will send text to a backend (currently, it just logs)

Avoid third-party voice libraries. Component must use React hooks and work with minimal permissions. Style it as a chat sidebar, located on the right side of the dashboard.