@use 'sass:color';
@use '../../../styles/variables' as vars;

.dashboard__sidebar {
  width: 280px;
  height: 100vh;
  background-color: vars.$primary-color;
  padding: vars.$spacing-lg;
  display: flex;
  flex-direction: column;
  border-right: 1px solid vars.$border-color;
  overflow: hidden;

  &-header {
    flex-shrink: 0;
    margin-bottom: vars.$spacing-sm;
  }

  &-title {
    font-size: vars.$font-size-xl;
    font-weight: vars.$font-weight-semibold;
    margin-bottom: vars.$spacing-md;
    color: vars.$text-color;
  }

  &-projects {
    flex: 1;
    overflow-y: auto;
    margin: -#{vars.$spacing-xs};
    padding: vars.$spacing-xs;
    min-height: 0;
  }

  &-footer {
    flex-shrink: 0;
    margin-top: vars.$spacing-lg;
    padding-top: vars.$spacing-md;
    border-top: 1px solid vars.$border-color;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;

    .dashboard__sidebar-logo {
      width: 120px;
      height: auto;
      opacity: 0.8;
    }
  }

  &-user-section {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-bottom: vars.$spacing-xl;
  }

  &-user-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &-user-greeting {
    font-size: vars.$font-size-sm;
    color: vars.$text-color;
    font-weight: vars.$font-weight-medium;
  }

  &-organization-section {
    margin-top: vars.$spacing-sm;
    width: 100%;
    max-width: 200px;
  }

  &-bucket-info {
    margin-top: vars.$spacing-xs;
    font-size: vars.$font-size-xs;
    color: vars.$color-gray-600;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: vars.$spacing-xs;
  }

  &-bucket-label {
    font-weight: vars.$font-weight-medium;
  }

  &-bucket-name {
    font-family: monospace;
    background-color: vars.$color-gray-100;
    padding: vars.$spacing-xs vars.$spacing-sm;
    border-radius: vars.$border-radius-sm;
    font-size: vars.$font-size-xs;
  }

  &-bucket-error {
    margin-top: vars.$spacing-xs;
    font-size: vars.$font-size-xs;
    color: vars.$color-error;
    text-align: center;
  }

  &-bucket-error-text {
    font-weight: vars.$font-weight-medium;
  }
}

.dashboard__project-item {
  margin-bottom: vars.$spacing-xs;
}

.dashboard__project-name {
  padding: vars.$spacing-sm vars.$spacing-md;
  border-radius: vars.$border-radius-sm;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: vars.$text-color;
  transition: all vars.$transition-fast;
  position: relative;
  font-size: vars.$font-size-sm;

  &:hover {
    background-color: vars.$color-background-hover;
  }

  &.expanded {
    font-weight: vars.$font-weight-medium;
  }

  &.active {
    background-color: vars.$color-gray-100;
    color: vars.$text-color;
    font-weight: vars.$font-weight-semibold;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background-color: vars.$primary-btn-color;
      border-radius: vars.$border-radius-sm;
    }
  }

  .dashboard__project-arrow {
    margin-right: vars.$spacing-sm;
    color: vars.$color-gray-500;
    transition: transform vars.$transition-fast;
  }
}

.dashboard__project-content {
  margin-left: calc(vars.$spacing-xl + vars.$spacing-md);
  margin-top: vars.$spacing-xs;
}

.dashboard__years-list {
  margin-bottom: 0;
}

.dashboard__year-item {
  padding: vars.$spacing-sm vars.$spacing-md;
  border-radius: vars.$border-radius-sm;
  cursor: pointer;
  color: vars.$text-color;
  transition: all vars.$transition-fast;
  position: relative;
  font-size: vars.$font-size-sm;

  &:hover {
    background-color: vars.$color-background-hover;
  }

  &.active {
    background-color: vars.$color-gray-100;
    color: vars.$text-color;
    font-weight: vars.$font-weight-semibold;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background-color: vars.$primary-btn-color;
      border-radius: vars.$border-radius-sm;
    }
  }
}

.dashboard__add-year {
  margin-top: 0;
  margin-bottom: 0;
}

.dashboard__add-year-item {
  padding: vars.$spacing-sm vars.$spacing-md;
  border-radius: vars.$border-radius-sm;
  cursor: pointer;
  color: vars.$color-gray-500;
  transition: all vars.$transition-fast;
  font-size: vars.$font-size-sm;

  &:hover {
    background-color: vars.$color-background-hover;
    color: vars.$color-gray-600;
  }
}

.dashboard__empty-state {
  text-align: center;
  padding: vars.$spacing-lg;
  color: vars.$color-gray-500;

  .dashboard__empty-title {
    font-size: vars.$font-size-base;
    font-weight: vars.$font-weight-medium;
    margin-bottom: vars.$spacing-xs;
  }

  .dashboard__empty-desc {
    font-size: vars.$font-size-sm;
  }
}

.dashboard__create-project {
  margin-top: vars.$spacing-md;
  padding-top: vars.$spacing-md;
  text-align: center;

  button {
    width: auto;
    justify-content: center;
  }
}
