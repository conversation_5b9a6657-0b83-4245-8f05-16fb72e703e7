@use '../../../styles/variables' as vars;

.output-panel {
  width: 100%;
  height: 100%;
  background-color: vars.$primary-color;
  border-top: 1px solid vars.$border-color;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &__actions {
    padding: vars.$spacing-md;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  &__files {
    flex: 1;
    overflow-y: auto;
    padding: vars.$spacing-md;
  }

  &__files-container {
    &--grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
      gap: vars.$spacing-md;
    }

    &--list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1px;
      max-height: 100%;
      overflow-y: auto;
      
      // Limit to 3 columns maximum using container queries
      container-type: inline-size;
      
      @container (min-width: 900px) {
        grid-template-columns: repeat(2, 1fr);
      }
      
      @container (min-width: 1400px) {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }
} 