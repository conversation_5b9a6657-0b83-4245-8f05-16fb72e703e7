import React, { useRef } from 'react';
import './FileUploader.scss';

interface FileUploaderProps {
  onUpload: (files: File[]) => Promise<void>;
  allowedFileTypes?: string[];
  maxFiles?: number;
}

const FileUploader: React.FC<FileUploaderProps> = ({ onUpload, allowedFileTypes = [], maxFiles = 5 }) => {
  const [isDragging, setIsDragging] = React.useState(false);
  const [isUploading, setIsUploading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFiles = (files: FileList): File[] => {
    const validFiles: File[] = [];
    const invalidFiles: string[] = [];

    Array.from(files).forEach(file => {
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      if (allowedFileTypes.length === 0 || allowedFileTypes.includes(fileExtension)) {
        validFiles.push(file);
      } else {
        invalidFiles.push(file.name);
      }
    });

    if (invalidFiles.length > 0) {
      setError(`Invalid file type(s): ${invalidFiles.join(', ')}`);
    }

    if (validFiles.length > maxFiles) {
      setError(`Maximum ${maxFiles} files allowed`);
      return validFiles.slice(0, maxFiles);
    }

    return validFiles;
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    setError(null);

    const files = validateFiles(e.dataTransfer.files);
    if (files.length === 0) return;

    try {
      setIsUploading(true);
      await onUpload(files);
    } catch (err) {
      setError('Failed to upload files. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileInput = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    setError(null);

    const files = validateFiles(e.target.files);
    if (files.length === 0) return;

    try {
      setIsUploading(true);
      await onUpload(files);
    } catch (err) {
      setError('Failed to upload files. Please try again.');
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    fileInputRef.current?.click();
  };

  return (
    <div className="file-uploader">
      <input
        ref={fileInputRef}
        type="file"
        multiple
        onChange={handleFileInput}
        accept={allowedFileTypes.join(',')}
        className="file-uploader__input"
      />
      <div
        className={`file-uploader__dropzone ${isDragging ? 'file-uploader__dropzone--active' : ''}`}
        onDragOver={e => {
          e.preventDefault();
          setIsDragging(true);
        }}
        onDragLeave={() => setIsDragging(false)}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <div className="file-uploader__label">
          <i className="bi bi-cloud-upload" style={{ fontSize: '2rem' }} />
          <span>Drag and drop files here or click to browse</span>
        </div>
        {allowedFileTypes.length > 0 && (
          <div className="file-uploader__types">Allowed file types: {allowedFileTypes.join(', ')}</div>
        )}
        {error && <div className="file-uploader__error">{error}</div>}
        {isUploading && <div className="file-uploader__uploading">Uploading files...</div>}
      </div>
    </div>
  );
};

export default FileUploader;
