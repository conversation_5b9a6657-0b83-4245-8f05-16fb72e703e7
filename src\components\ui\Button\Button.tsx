import React from 'react';
import LoadingSpinner from '@/assets/icons/spinner.svg?react';
import './Button.scss';

export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'gray';
export type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  className?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  iconOnly?: boolean;
  loading?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size,
  className = '',
  icon,
  iconPosition = 'left',
  iconOnly = false,
  loading = false,
  disabled,
  ...props
}) => {
  // Accessibility: iconOnly buttons must have aria-label
  if (iconOnly && !props['aria-label']) {
    console.warn('Icon-only buttons must have an aria-label for accessibility.');
  }

  const isDisabled = disabled || loading;

  return (
    <button
      className={`button button--${variant} ${size ? `button--${size}` : ''} ${iconOnly ? 'button--icon-only' : ''} ${loading ? 'button--loading' : ''} ${className}`.trim()}
      disabled={isDisabled}
      {...props}
    >
      {loading && (
        <span className="button__spinner" aria-hidden="true">
          <LoadingSpinner className="button__spinner-svg" />
        </span>
      )}
      {icon && iconPosition === 'left' && !iconOnly && <span className="button__icon button__icon--left">{icon}</span>}
      {!iconOnly && <span className="button__text">{children}</span>}
      {icon && iconPosition === 'right' && !iconOnly && (
        <span className="button__icon button__icon--right">{icon}</span>
      )}
      {iconOnly && icon && !children && !loading && <span className="button__icon button__icon--center">{icon}</span>}
    </button>
  );
};

export default Button;
