import { CognitoIdentityClient } from '@aws-sdk/client-cognito-identity';
import { StartDocumentAnalysisCommand, TextractClient } from '@aws-sdk/client-textract';
import { fromCognitoIdentityPool } from '@aws-sdk/credential-provider-cognito-identity';
import { NextResponse } from 'next/server';

const cognitoIdentityClient = new CognitoIdentityClient({
  region: process.env.AWS_REGION,
});

const getTextractClient = async (idToken: string) => {
  const credentialsProvider = fromCognitoIdentityPool({
    client: cognitoIdentityClient,
    identityPoolId: process.env.COGNITO_IDENTITY_POOL_ID!,
    logins: {
      [`cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.COGNITO_USER_POOL_ID}`]: idToken,
    },
  });

  return new TextractClient({
    region: process.env.AWS_REGION,
    credentials: credentialsProvider,
  });
};

export async function POST(request: Request) {
  try {
    const { filePath, idToken } = await request.json();

    if (!idToken) {
      return NextResponse.json({ error: 'No ID token provided' }, { status: 401 });
    }

    const textractClient = await getTextractClient(idToken);
    const command = new StartDocumentAnalysisCommand({
      DocumentLocation: {
        S3Object: {
          Bucket: process.env.AWS_S3_BUCKET,
          Name: filePath,
        },
      },
      FeatureTypes: ['FORMS', 'TABLES'],
    });

    const response = await textractClient.send(command);
    return NextResponse.json({ jobId: response.JobId });
  } catch (error) {
    console.error('Error starting document analysis:', error);
    return NextResponse.json({ error: 'Failed to start document analysis' }, { status: 500 });
  }
}
