import React, { useState, useRef, useEffect } from 'react';
import './UserMenu.scss';

interface UserMenuProps {
  userName: string;
  avatarUrl?: string;
  onSignOut: () => void;
  onProfileSettings?: () => void;
}

export const UserMenu: React.FC<UserMenuProps> = ({ userName, avatarUrl, onSignOut, onProfileSettings }) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const defaultAvatar =
    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU3RUIiLz4KPHBhdGggZD0iTTIwIDEwQzIyLjIwOTEgMTAgMjQgMTEuNzkwOSAyNCAxNEMyNCAxNi4yMDkxIDIyLjIwOTEgMTggMjAgMThDMTcuNzkwOSAxOCAxNiAxNi4yMDkxIDE2IDE0QzE2IDExLjc5MDkgMTcuNzkwOSAxMCAyMCAxMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTI4IDMwQzI4IDI2LjY4NjMgMjQuNDE4MyAyNCAyMCAyNEMxNS41ODE3IDI0IDEyIDI2LjY4NjMgMTIgMzBIMjhaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo=';

  return (
    <div className="user-menu" ref={menuRef}>
      <div className="user-menu__trigger flex items-center gap-3 cursor-pointer" onClick={() => setIsOpen(!isOpen)}>
        <div className="user-menu__avatar">
          <img
            src={avatarUrl || defaultAvatar}
            alt="User Avatar"
            className="user-menu__avatar-img"
            height={48}
            width={48}
          />
        </div>
      </div>

      {isOpen && (
        <div className="user-menu__dropdown">
          <button className="user-menu__item" onClick={onProfileSettings}>
            Settings
          </button>
          <button className="user-menu__item" onClick={onSignOut}>
            Sign Out
          </button>
        </div>
      )}
    </div>
  );
};

export default UserMenu;
