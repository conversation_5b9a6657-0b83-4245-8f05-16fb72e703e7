import { Organization } from '@/types/organization.types';
import { Building2, ChevronDown } from 'lucide-react';
import React, { useState } from 'react';
import './OrganizationSelector.scss';

interface OrganizationSelectorProps {
  currentOrganization: Organization | null;
  availableOrganizations: Organization[];
  onOrganizationChange: (organization: Organization) => void;
  isLoading?: boolean;
  error?: string | null;
}

const OrganizationSelector: React.FC<OrganizationSelectorProps> = ({
  currentOrganization,
  availableOrganizations,
  onOrganizationChange,
  isLoading = false,
  error = null,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleOrganizationSelect = (organization: Organization) => {
    onOrganizationChange(organization);
    setIsOpen(false);
  };

  if (isLoading) {
    return (
      <div className="organization-selector">
        <div className="organization-selector__loading">
          <div className="organization-selector__loading-text">Loading organizations...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="organization-selector">
        <div className="organization-selector__error">
          <div className="organization-selector__error-text">{error}</div>
        </div>
      </div>
    );
  }

  if (!currentOrganization || availableOrganizations.length === 0) {
    return (
      <div className="organization-selector">
        <div className="organization-selector__empty">
          <div className="organization-selector__empty-text">No organizations available</div>
        </div>
      </div>
    );
  }

  return (
    <div className="organization-selector">
      <div className={`organization-selector__trigger ${isOpen ? 'open' : ''}`} onClick={() => setIsOpen(!isOpen)}>
        <div className="organization-selector__current">
          <Building2 size={16} className="organization-selector__icon" />
          <span className="organization-selector__name">{currentOrganization.displayName}</span>
        </div>
        <ChevronDown size={16} className="organization-selector__arrow" />
      </div>
      {isOpen && (
        <div className="organization-selector__dropdown">
          <div className="organization-selector__dropdown-header">
            <span className="organization-selector__dropdown-title">Select Organization</span>
          </div>
          <div className="organization-selector__dropdown-list">
            {availableOrganizations.map(organization => (
              <div
                key={organization.id}
                className={`organization-selector__option ${
                  organization.id === currentOrganization.id ? 'active' : ''
                }`}
                onClick={() => handleOrganizationSelect(organization)}
              >
                <Building2 size={16} className="organization-selector__option-icon" />
                <span className="organization-selector__option-name">{organization.displayName}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default OrganizationSelector;
