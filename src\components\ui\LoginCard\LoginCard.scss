@use "../../../styles/variables" as vars;

.login-card {
  background-color: vars.$primary-color;
  border-radius: vars.$border-radius;
  box-shadow: vars.$shadow-lg;
  padding: vars.$spacing-xl;
  max-width: 400px;
  width: 100%;
  text-align: center;

  &__title {
    font-size: vars.$font-size-xl;
    font-weight: vars.$font-weight-semibold;
    color: vars.$text-color;
    margin-bottom: vars.$spacing-lg;
  }

  &__form {
    display: flex;
    flex-direction: column;
    gap: vars.$spacing-md;
  }

  &__input {
    padding: vars.$spacing-sm vars.$spacing-md;
    border: 1px solid vars.$border-color;
    border-radius: vars.$border-radius-sm;
    font-size: vars.$font-size-base;
    transition: border-color vars.$transition-fast;

    &:focus {
      outline: none;
      border-color: vars.$primary-btn-color;
    }
  }

  &__button {
    background-color: #00b8e6;
    color: vars.$text-color;
    border: none;
    padding: vars.$spacing-sm vars.$spacing-md;
    border-radius: vars.$border-radius-sm;
    font-size: vars.$font-size-base;
    font-weight: vars.$font-weight-medium;
    cursor: pointer;
    transition: background-color vars.$transition-fast;

    &:hover {
      background-color: #00b8e6;
    }
  }

  &__error {
    color: vars.$color-error;
    font-size: vars.$font-size-sm;
    margin-top: vars.$spacing-sm;
  }
} 