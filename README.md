# Project Documentation

## Project Structure

This is a React-based web application built with Vite, TypeScript, and Tailwind CSS. Below is an explanation of the main directories and files:

### Root Directory
- `package.json` - Contains project dependencies and scripts
- `vite.config.ts` - Vite configuration file
- `tsconfig.json` - TypeScript configuration
- `tailwind.config.ts` - Tailwind CSS configuration
- `index.html` - Main HTML entry point
- `shadcn-components.json` - Shadcn UI components configuration
- `.eslintrc.js` - ESLint configuration

### Source Directory (`src/`)
- `app.tsx` - Main application component
- `main.tsx` - Application entry point
- `app.css` - Main application styles
- `global.css` - Global styles and CSS variables
- `components/` - Reusable UI components
- `contexts/` - React context providers
- `data/` - Static data and mock data
- `hooks/` - Custom React hooks
- `lib/` - Utility libraries and configurations
- `pages/` - Page components
- `services/` - API service integrations
- `styles/` - Global styles and CSS
- `types/` - TypeScript type definitions
- `utils/` - Utility functions
- `views/` - View components

## API Key Requirements

The project has several service integrations that require API keys:

### Authentication Service (`src/services/authentication.service.ts`)
- This service handles user authentication
- API keys should be added in the environment variables:
  - `VITE_AUTH_API_KEY`

### File Storage Service (`src/services/file-storage.service.ts`)
- This service handles file storage and management
- API keys should be added in the environment variables:
  - `VITE_DRIVE_API_KEY`

### Speech Recognition Service (`src/services/speech-recognition.service.ts`)
- This service handles voice-related functionality
- API keys should be added in the environment variables:
  - `VITE_VOICE_API_KEY`

## Setting Up Environment Variables

1. Create a `.env` file in the root directory
2. Add the following variables:
```
VITE_AUTH_API_KEY=your_auth_api_key
VITE_DRIVE_API_KEY=your_drive_api_key
VITE_VOICE_API_KEY=your_voice_api_key
```

Note: Make sure to add `.env` to your `.gitignore` file to prevent committing sensitive information.

## Running the Project

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Build for production:
```bash
npm run build
``` 
## Project Guidelines

### Src Structure
```
src/
├── assets/
├── components/
│   ├── ui/              # Reusable UI components (buttons, inputs, etc.)
│   │   └── [ComponentName]/
│   │       ├── ComponentName.tsx  # Main component file
│   │       └── ComponentName.scss # Component styles
│   ├── layout/          # Layout components (Header, Sidebar, etc.)
│   │   └── [ComponentName]/
│   │       ├── ComponentName.tsx
│   │       └── ComponentName.scss
│   └── [ComponentName]/ # Feature-specific components
│       ├── ComponentName.tsx
│       └── ComponentName.scss
├── views/              # View components (1:1 with routes)
│   └── [ViewName]/
│       ├── ViewName.tsx
│       └── ViewName.scss
├── hooks/             # Global custom hooks
├── contexts/          # Global context providers
├── services/          # API and service layer
├── utils/             # Utility functions
├── types/             # Global TypeScript types
├── styles/           # Global styles
│   ├── base/         # Base styles, variables, mixins
│   ├── components/   # Global component styles
│   └── themes/       # Theme configurations
├── lib/              # Third-party library configurations
├── App.css
├── App.tsx
├── global.tsx
├── main.tsx
└── vite-env.d.ts
```

### Styling Guidelines

#### SCSS/BEM Usage
Use SCSS with BEM for:
- Complex components with multiple nested elements
- Components needing complex state variations
- Reusable patterns and shared component styles
- Layout patterns repeating across the app

Example:
```scss
.component {
  &__element { /* ... */ }
  &--modifier { /* ... */ }
}
```

#### Tailwind Usage
Use Tailwind for:
- Simple components with minimal styling needs
- One-off UI elements
- Quick prototypes
- Responsive adjustments

Example:
```tsx
<div className="flex items-center gap-2 p-4">
```

### File Naming Conventions

- **Components**: 
  - Main component file: `ComponentName.tsx` (PascalCase)
  - Component directory: `ComponentName/` (PascalCase)
  - Component styles: `ComponentName.scss` (PascalCase)
  - Example: `Button/Button.tsx` and `Button/Button.scss`
- **Global SCSS Partials**: `_filename.scss` (prefixed with underscore)
- **Utilities**: camelCase (`formatDate.ts`)

### Component Organization

```tsx
// Component Structure
/ComponentName/
  ├── ComponentName.tsx  // Main component
  ├── ComponentName.scss // Component styles
  ├── ComponentName.test.tsx  // Tests
  └── components/       // Sub-components (if needed)

// Component Code Example
import React from 'react';
import './ComponentName.scss';

interface ComponentProps {
  // Props interface
}

export const Component: React.FC<ComponentProps> = ({ prop }) => {
  return (
    <div className="component">
      {/* Component JSX */}
    </div>
  );
};
```

### Style Implementation
Ensure all color values are referencing a variable and never a hardcoded hexadecimal.

```scss
// Global Variables (_variables.scss)
$color-primary: #00D1FF;
$spacing-unit: 0.25rem;

// Mixins (_mixins.scss)
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Component Styles
.component {
  @include flex-center;
  
  &__element {
    // Element styles
  }
  
  &--modifier {
    // Modifier styles
  }
}
```

### Implementation Rules

1. **Style Imports**:
   - SCSS files should only be imported in their corresponding component
   - Global styles should be imported in the app root

2. **Component Organization**:
   - One main component per file
   - Related sub-components in the same directory
   - Shared components go in `ui/`

3. **Style Precedence**:
   ```tsx
   <div 
     className={`
       component           // BEM base class
       ${variant === 'grid' ? 'component--grid' : ''}  // BEM modifier
       md:gap-6           // Tailwind utility
     `}
   >
   ```
