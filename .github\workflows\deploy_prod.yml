name: Elastic Beanstalk Prod Deployment

on:
  push:
    branches: main

jobs:
  build-and-deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install Dependencies
        run: npm install

      - name: Build Frontend
        env:
          # AWS S3 Configuration
          VITE_AWS_S3_BUCKET_REGION: ${{ secrets.AWS_S3_BUCKET_REGION }}
          VITE_AWS_S3_BUCKET_NAME: ${{ secrets.AWS_S3_BUCKET_NAME }}
          
          # AWS Cognito Configuration
          VITE_AWS_REGION: ${{ secrets.AWS_REGION }}
          VITE_COGNITO_USER_POOL_ID: ${{ secrets.COGNITO_USER_POOL_ID }}
          VITE_COGNITO_CLIENT_ID: ${{ secrets.COGNITO_CLIENT_ID }}
          VITE_COGNITO_IDENTITY_POOL_ID: ${{ secrets.COGNITO_IDENTITY_POOL_ID }}
          
          # Explain Backend URL
          VITE_EXPLAIN_BACKEND_URL: ${{ secrets.PROD_BACKEND_URL }}
          APP_ENV: 'production'
        run: npm run build

      - name: Zip Frontend
        run: zip -r frontend-prod-${{ github.sha }}.zip .

      - name: Deploy to Elastic Beanstalk
        uses: einaregilsson/beanstalk-deploy@v21
        with:
          application_name: Explain Production
          environment_name: explain-prod-env
          version_label: frontend-prod-${{ github.sha }}
          region: ${{ secrets.AWS_REGION }}
          aws_access_key: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          deployment_package: frontend-prod-${{ github.sha }}.zip
          existing_bucket_name: elasticbeanstalk-us-east-1-084375554197
          wait_for_deployment: false
          use_existing_version_if_available: true