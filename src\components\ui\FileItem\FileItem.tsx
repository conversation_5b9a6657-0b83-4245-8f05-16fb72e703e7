import { FileItem as FileItemType } from '@/types';
import { Download, Loader2, More<PERSON><PERSON>ical, ShieldCheck, Trash2 } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import FilePreviewModal from '../FilePreviewModal/FilePreviewModal';
import { ViewMode } from '../ViewToggle/ViewToggle';
import './FileItem.scss';

interface FileItemProps {
  file: FileItemType;
  onAction: (action: 'download' | 'delete', file: FileItemType) => void;
  isProcessed?: boolean;
  isChecking?: boolean;
  viewMode?: ViewMode;
}

const FileItem: React.FC<FileItemProps> = ({ file, onAction, isProcessed, isChecking, viewMode }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const itemRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(!isMenuOpen);
  };

  const handleFileClick = (e: React.MouseEvent) => {
    // Only open preview if clicking the main file item area (not the menu)
    if (e.target === itemRef.current || itemRef.current?.contains(e.target as Node)) {
      setIsPreviewOpen(true);
    }
  };

  const handleAction = (action: 'download' | 'delete') => {
    onAction(action, file);
    setIsMenuOpen(false);
  };

  return (
    <>
      <div className={`file-item ${viewMode === 'list' ? 'file-item--list' : ''}`} onClick={handleFileClick} ref={itemRef}>
        <div className="file-item__content">
          <span className="file-item__name">{file.name}</span>
        </div>
        <div className="file-item__actions" ref={menuRef}>
          {isChecking ? (
            <Loader2 size={16} className="file-item__shield file-item__shield--loading" />
          ) : (
            isProcessed && <ShieldCheck size={16} className="file-item__shield" />
          )}
          <button className="file-item__menu-button" onClick={handleMenuClick} aria-label="File actions">
            <MoreVertical size={16} />
          </button>
          {isMenuOpen && (
            <div className="file-item__menu">
              <button className="file-item__menu-item" onClick={() => handleAction('download')}>
                <Download size={16} />
                Download
              </button>
              <button
                className="file-item__menu-item file-item__menu-item--danger"
                onClick={() => handleAction('delete')}
              >
                <Trash2 size={16} />
                Delete
              </button>
            </div>
          )}
        </div>
      </div>
      <FilePreviewModal isOpen={isPreviewOpen} onClose={() => setIsPreviewOpen(false)} file={file} />
    </>
  );
};

export default FileItem;
