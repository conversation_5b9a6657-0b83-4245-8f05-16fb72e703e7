---
description: 
globs: 
alwaysApply: true
---
# Feature: File Upload Rules

At specific folder levels, file uploads are allowed with format restrictions:

- General Ledger: only `.xlsx`, `.xls`, `.csv` files allowed
- Source Documents: only `.pdf`, `.png`, `.jpg`, `.jpeg` allowed
- Output: read-only, show downloadable file list, no uploads

Use a reusable `FileUploader` component in `components/ui/FileUploader/` with a prop `allowedFileTypes`. Upload logic must route through `services/file-storage.service.ts`.