@use "sass:color";
@use "@/styles/variables" as *;

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-sm $spacing-md;
  border: 1px solid $color-border;
  border-radius: $border-radius;
  background-color: $color-background;
  transition: all $transition-fast;
  font-family: 'Poppins', system-ui, sans-serif;
  cursor: pointer;
  min-height: 60px;
  max-height: 70px;

  &:hover {
    background-color: $color-background-hover;
    border-color: $primary-btn-color;
  }

  &--list {
    min-height: auto;
    max-height: none;
    padding: $spacing-sm $spacing-md;
    height: auto;
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-top: none;
    border-bottom: 1px solid $color-gray-200;

    &:hover {
      background-color: $color-gray-100;
      border-color: $color-gray-300;
      border-radius: $border-radius-sm;
    }

    .file-item__name {
      white-space: normal;
      line-height: 1.4;
      font-size: $font-size-xs;
    }
  }

  &__content {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    flex: 1;
    min-width: 0;
    overflow: hidden;
  }

  &__name {
    color: $color-text;
    font-size: $font-size-xs;
    font-family: 'Poppins', system-ui, sans-serif;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    line-height: 1.2;
  }

  &__shield {
    color: $color-success;
    display: flex;
    align-items: center;

    &--loading {
      color: $color-text;
      animation: spin 1s linear infinite;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  &__actions {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &__menu-button {
    background-color: transparent;
    color: $color-text;
    border: none;
    border-radius: $border-radius;
    padding: $spacing-xs;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all $transition-fast;
    font-family: 'Poppins', system-ui, sans-serif;

    &:hover {
      background-color: rgba($primary-btn-color, 0.1);
      color: $primary-btn-color;
    }
  }

  &__menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: $color-background;
    border: 1px solid $color-border;
    border-radius: $border-radius;
    box-shadow: $shadow-md;
    z-index: 100;
    min-width: 150px;
    font-family: 'Poppins', system-ui, sans-serif;
  }

  &__menu-item {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    padding: $spacing-sm $spacing-md;
    width: 100%;
    text-align: left;
    background: none;
    border: none;
    color: $color-text;
    cursor: pointer;
    transition: background-color $transition-fast;
    font-family: 'Poppins', system-ui, sans-serif;

    &:hover {
      background-color: $color-background-hover;
    }

    &--danger {
      color: $color-error;

      &:hover {
        background-color: rgba($color-error, 0.1);
      }
    }
  }
} 