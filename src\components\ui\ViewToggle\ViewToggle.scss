@use "@/styles/variables" as *;

.view-toggle {
  display: flex;
  align-items: center;
  background-color: $color-gray-200;
  border-radius: $border-radius;
  padding: 2px;
  border: 1px solid $color-gray-300;

  &__button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $spacing-xs;
    border: none;
    background: transparent;
    border-radius: $border-radius-sm;
    cursor: pointer;
    transition: all $transition-fast;
    color: $color-gray-600;
    min-width: 28px;
    min-height: 28px;

    &:hover {
      background-color: rgba($primary-btn-color, 0.1);
      color: $primary-btn-color;
    }

    &--active {
      background-color: $color-gray-500;
      color: $color-background;
      box-shadow: $shadow-sm;

      &:hover {
        background-color: #6366f1;
      }
    }
  }
} 